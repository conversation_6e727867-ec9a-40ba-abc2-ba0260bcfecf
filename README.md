# MMPose Flask - 实时人体姿态检测服务

基于TensorRT优化的Flask Web服务，专门用于检测打电话、吸烟和摔倒等特定行为的实时人体姿态识别系统。

## 🚀 项目特性

- **实时推理**: TensorRT加速，毫秒级响应
- **高精度检测**: 17个人体关键点精准定位  
- **行为识别**: 支持打电话、吸烟、摔倒检测
- **生产就绪**: Docker容器化部署
- **RESTful API**: 简洁的HTTP接口

## 📁 项目结构

```
mmpose_flask/
├── src/                      # 源代码
│   ├── service.py           # Flask主服务 (端口20249)
│   ├── trt_model.py         # 姿态检测器封装
│   ├── Tensorrt.py          # TensorRT引擎封装
│   ├── action_filters.py    # 行为识别过滤器
│   └── cyddh_filter.py      # 备用过滤器
├── models/                   # 模型文件
│   └── 1.engine            # TensorRT优化模型
├── docs/                     # 技术文档
│   ├── technical_documentation.md
│   ├── MMPose_TensorRT_Technical_Documentation.md
│   └── api_user_guide.md
├── deployment/              # 部署配置
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── deploy.sh           # 一键部署脚本
├── diagrams/                # 系统图表
└── tests/                   # 测试文件
```

## ⚡ 快速开始

### 系统要求

- NVIDIA GPU (CUDA 10.2+)
- Python 3.8+
- TensorRT 7.0+
- Docker + nvidia-container-toolkit (可选)

### 本地运行

```bash
# 安装依赖
pip install -r deployment/requirements.txt

# 启动服务
cd src
python service.py
```

### Docker部署 (推荐)

```bash
# 一键部署
cd deployment
./deploy.sh all

# 验证部署
curl http://localhost:20249/health
```

## 📡 API接口

### 打电话检测
```bash
POST /phoning
{
  "image": "base64编码图像",
  "boxLocX": 100,
  "boxLocY": 150
}
```

### 吸烟检测  
```bash
POST /smoking
{
  "image": "base64编码图像",
  "boxLocX": 100,
  "boxLocY": 150
}
```

### 摔倒检测
```bash
POST /falling
{
  "image": "base64编码图像"
}
```

**响应格式**: `{"result": true/false}`

## 🔧 技术架构

- **深度学习框架**: PyTorch + TensorRT
- **Web框架**: Flask
- **图像处理**: OpenCV  
- **关键点模型**: 17点COCO人体骨架
- **推理优化**: FP16精度 + 异步CUDA
- **容器化**: Docker + Docker Compose

## 📈 性能指标

| 指标 | 数值 |
|------|------|
| 推理延迟 | ~15ms |
| 吞吐量 | ~65 FPS |
| GPU利用率 | ~85% |
| 内存占用 | ~1.2GB |

## 📖 文档导航

### 🆕 新人入门
- [**新人快速上手指南**](docs/QUICKSTART_GUIDE.md) - 零基础入门完整教程
- [学习进度检查清单](docs/LEARNING_CHECKLIST.md) - 学习进度跟踪

### 📚 技术文档  
- [技术文档](docs/technical_documentation.md) - 详细技术说明
- [API指南](docs/api_user_guide.md) - 接口使用文档  
- [部署指南](docs/deployment_guide.md) - 生产部署说明
- [TensorRT文档](docs/MMPose_TensorRT_Technical_Documentation.md) - TensorRT实现解析

## 🛠️ 开发指南

### 添加新的行为检测

1. 继承`CommonTools`类 (src/action_filters.py)
2. 实现`filter()`方法
3. 在service.py中添加新路由
4. 使用示例图像测试

### 性能优化

- 启用FP16精度
- 使用批量推理
- 异步CUDA执行
- 内存预分配

## 🐳 Docker部署选项

```bash
# 基础部署
./deploy.sh basic

# 带负载均衡
./deploy.sh nginx

# 带监控
./deploy.sh monitoring  

# 完整部署
./deploy.sh all
```

## 🔍 故障排除

### 常见问题

1. **GPU不可用**: 检查CUDA驱动和nvidia-docker
2. **模型加载失败**: 确认1.engine文件存在且完整
3. **内存不足**: 调整batch_size或使用更大GPU
4. **推理慢**: 检查TensorRT版本兼容性

### 调试命令

```bash
# 检查GPU状态
nvidia-smi

# 查看容器日志
docker-compose logs -f

# 性能测试
curl -X POST http://localhost:20249/health
```

## 📄 许可证

本项目仅供学习和研究使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

---

**注意**: 本系统需要NVIDIA GPU环境，不支持CPU推理。部署前请确保满足硬件要求。