#!/bin/bash

# MMPose Flask Docker 部署脚本
# 使用方法: ./deploy.sh [basic|nginx|monitoring|all]

set -e

DEPLOYMENT_TYPE=${1:-basic}
PROJECT_NAME="mmpose-flask"

echo "=========================================="
echo "MMPose Flask Docker 部署脚本"
echo "部署类型: $DEPLOYMENT_TYPE"
echo "=========================================="

# 检查系统要求
check_requirements() {
    echo "检查系统要求..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker 未安装"
        exit 1
    fi
    echo "✅ Docker: $(docker --version)"
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose 未安装"
        exit 1
    fi
    echo "✅ Docker Compose: $(docker-compose --version)"
    
    # 检查NVIDIA Docker支持
    if ! docker run --rm --gpus all nvidia/cuda:11.2-base nvidia-smi &> /dev/null; then
        echo "❌ NVIDIA Docker 支持不可用"
        echo "请安装 nvidia-container-toolkit"
        exit 1
    fi
    echo "✅ NVIDIA Docker 支持正常"
    
    # 检查关键文件
    REQUIRED_FILES=("service.py" "trt_model.py" "1.engine" "Dockerfile" "docker-compose.yml")
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -f "$file" ]; then
            echo "❌ 缺少关键文件: $file"
            exit 1
        fi
    done
    echo "✅ 所有关键文件存在"
}

# 创建目录结构
setup_directories() {
    echo "创建目录结构..."
    mkdir -p logs temp models monitoring/{grafana,prometheus}
    echo "✅ 目录创建完成"
}

# 部署服务
deploy_service() {
    echo "开始部署服务..."
    
    case $DEPLOYMENT_TYPE in
        "basic")
            echo "基础部署模式..."
            docker-compose up -d mmpose-flask
            ;;
        "nginx")
            echo "带Nginx负载均衡部署..."
            docker-compose --profile with-nginx up -d
            ;;
        "monitoring")
            echo "带监控的部署..."
            docker-compose --profile monitoring up -d
            ;;
        "all")
            echo "完整部署（包含Nginx和监控）..."
            docker-compose --profile with-nginx --profile monitoring up -d
            ;;
        *)
            echo "❌ 未知部署类型: $DEPLOYMENT_TYPE"
            echo "支持的类型: basic, nginx, monitoring, all"
            exit 1
            ;;
    esac
}

# 验证部署
verify_deployment() {
    echo "验证部署状态..."
    sleep 10  # 等待服务启动
    
    # 检查容器状态
    if ! docker-compose ps | grep -q "Up"; then
        echo "❌ 容器启动失败"
        docker-compose logs
        exit 1
    fi
    echo "✅ 容器运行正常"
    
    # 检查健康状态
    MAX_RETRIES=30
    RETRY_COUNT=0
    
    while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        if curl -f http://localhost:20249/health &> /dev/null; then
            echo "✅ 服务健康检查通过"
            break
        fi
        echo "⏳ 等待服务就绪... ($RETRY_COUNT/$MAX_RETRIES)"
        sleep 2
        RETRY_COUNT=$((RETRY_COUNT + 1))
    done
    
    if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
        echo "❌ 服务健康检查超时"
        echo "容器日志:"
        docker-compose logs mmpose-flask
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    echo "=========================================="
    echo "✅ 部署完成!"
    echo "=========================================="
    
    echo "服务信息:"
    echo "- API服务: http://localhost:20249"
    echo "- 健康检查: http://localhost:20249/health"
    
    case $DEPLOYMENT_TYPE in
        "nginx"|"all")
            echo "- Nginx代理: http://localhost:80"
            ;;
    esac
    
    case $DEPLOYMENT_TYPE in
        "monitoring"|"all")
            echo "- Grafana监控: http://localhost:3000 (admin/admin123)"
            echo "- Prometheus: http://localhost:9090"
            ;;
    esac
    
    echo ""
    echo "常用命令:"
    echo "- 查看状态: docker-compose ps"
    echo "- 查看日志: docker-compose logs -f"
    echo "- 停止服务: docker-compose down"
    echo "- 重启服务: docker-compose restart"
    echo ""
    
    echo "API测试示例:"
    cat << 'EOF'
curl -X POST http://localhost:20249/phoning \
  -H "Content-Type: application/json" \
  -d '{
    "image": "base64_encoded_image_data",
    "boxLocX": 100,
    "boxLocY": 150
  }'
EOF
    echo ""
}

# 主执行流程
main() {
    check_requirements
    setup_directories
    deploy_service
    verify_deployment
    show_deployment_info
}

# 错误处理
trap 'echo "❌ 部署过程中出现错误"; exit 1' ERR

# 运行主程序
main

echo "=========================================="
echo "🎉 MMPose Flask 部署成功完成!"
echo "=========================================="