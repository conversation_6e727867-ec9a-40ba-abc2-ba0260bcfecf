version: '3.8'

services:
  mmpose-flask:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: mmpose-flask-service
    ports:
      - "20249:20249"
    volumes:
      # 挂载模型文件目录（如果需要动态更新模型）
      - ./models:/app/models:ro
      # 挂载日志目录
      - ./logs:/app/logs
      # 挂载临时文件目录
      - ./temp:/app/temp
    environment:
      # CUDA相关环境变量
      - CUDA_VISIBLE_DEVICES=0
      - NVIDIA_VISIBLE_DEVICES=0
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      # Flask相关环境变量
      - FLASK_ENV=production
      - PYTHONUNBUFFERED=1
      - TRT_LOGGER_LEVEL=WARNING
    deploy:
      resources:
        # 预留GPU资源
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        # 限制资源使用
        limits:
          memory: 8G
          cpus: '4.0'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:20249/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"
    networks:
      - mmpose-network

  # 可选：添加负载均衡器 (nginx)
  nginx:
    image: nginx:alpine
    container_name: mmpose-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - mmpose-flask
    networks:
      - mmpose-network
    profiles:
      - with-nginx

  # 可选：添加监控服务 (prometheus + grafana)
  prometheus:
    image: prom/prometheus:latest
    container_name: mmpose-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - mmpose-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: mmpose-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    networks:
      - mmpose-network
    profiles:
      - monitoring

networks:
  mmpose-network:
    driver: bridge

volumes:
  prometheus_data:
  grafana_data: