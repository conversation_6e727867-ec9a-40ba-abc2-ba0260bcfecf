# 基于 NVIDIA TensorRT 官方镜像
FROM nvcr.io/nvidia/tensorrt:22.12-py3

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH="/app:${PYTHONPATH}"
ENV CUDA_VISIBLE_DEVICES=0
ENV PYTHONUNBUFFERED=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libgomp1 \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 升级pip并安装Python依赖
RUN pip install --upgrade pip

# 安装核心依赖
RUN pip install \
    flask==2.3.3 \
    opencv-python==******** \
    numpy==1.24.3 \
    torch==2.0.1 \
    torchvision==0.15.2 \
    Pillow==10.0.1 \
    gunicorn==21.2.0

# 复制应用程序文件
COPY *.py /app/
COPY *.engine /app/
COPY *.jpg /app/

# 创建必要的目录
RUN mkdir -p /app/logs /app/temp

# 设置文件权限
RUN chmod +x /app/*.py

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:20249/health || exit 1

# 添加健康检查端点到Flask应用
RUN echo "from flask import Flask\n\
@app.route('/health')\n\
def health_check():\n\
    return {'status': 'healthy', 'service': 'mmpose_flask'}" >> /app/health_check.py

# 暴露端口
EXPOSE 20249

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "Starting MMPose Flask Service..."\n\
echo "GPU Info:"\n\
nvidia-smi\n\
echo "Python Dependencies:"\n\
pip list | grep -E "(torch|tensorrt|opencv|flask|numpy)"\n\
echo "Starting application on port 20249..."\n\
exec python service.py' > /app/start.sh && chmod +x /app/start.sh

# 使用启动脚本
CMD ["/app/start.sh"]