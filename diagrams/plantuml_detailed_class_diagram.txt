@startuml MMPose Flask 详细类图

' 设置样式
skinparam classAttributeIconSize 0
skinparam classFontStyle bold
skinparam classBackgroundColor LightBlue
skinparam classBorderColor DarkBlue
skinparam packageBackgroundColor LightGray
skinparam packageBorderColor Gray

' 定义包
package "Flask Web 服务" {
    class Flask {
        + run(host: str, port: int)
        + route(rule: str, methods: list)
    }
}

package "PyTorch 框架" {
    abstract class "torch.nn.Module" {
        + forward(inputs)
    }
}

package "TensorRT 集成" {
    class TrtModelMMPose {
        - engine: trt.ICudaEngine
        - context: trt.IExecutionContext
        - _input_names: list
        - _output_names: list
        + __init__(engine: Union[str, trt.ICudaEngine], output_names: Optional[Sequence[str]])
        + forward(inputs: Dict[str, torch.Tensor]): torch.Tensor
    }
    
    class "select_device" << function >> {
        + select_device(device: str, apex: bool, batch_size: int): torch.device
    }
}

package "姿态检测" {
    class PoseDetector {
        - input_shape: list
        - device: torch.device
        - weights: str
        - model: TrtModelMMPose
        + __init__(weights: str, device: torch.device)
        + _warm_up()
        + predict(img: np.ndarray): torch.Tensor
        + postprocessing(outputs: torch.Tensor, ori_img_w: int, ori_img_h: int): Tuple[list, list]
    }
    
    class "preprocessing" << function >> {
        + preprocessing(ori_img: np.ndarray): torch.Tensor
    }
}

package "行为过滤器" {
    abstract class CommonTools {
        + __init__()
        + calculate_angle(vecA: np.ndarray, vecB: np.ndarray): float
        + calculate_distance(pointA: np.ndarray, pointB: np.ndarray): float
        + judge_position()
    }
    
    class SmokingFilter {
        - smoking_threshold: float
        - keypoints: np.ndarray
        - scores: list
        - center_point: np.ndarray
        + __init__(keypoints: list, scores: list, center_point: list)
        + set_score_thresh(thresh: float)
        + filter(angle_thresh: float = 60): bool
    }
    
    class PhoningFilter {
        - phoning_threshold: float
        - keypoints: np.ndarray
        - scores: list
        - center_point: np.ndarray
        + __init__(keypoints: list, scores: list, center_point: list)
        + set_score_thresh(thresh: float)
        + filter(angle_thresh: float = 60): bool
    }
    
    class FallingFilter {
        - falling_threshold: float
        - keypoints: np.ndarray
        - scores: list
        + __init__(keypoints: list, scores: list)
        + set_score_thresh(thresh: float)
        + filter(angle_thresh: float = 60): bool
    }
}

package "服务接口" {
    class "phoning_detect" << function >> {
        + phoning_detect(): Response
    }
    
    class "smoking_detect" << function >> {
        + smoking_detect(): Response
    }
    
    class "falling_detect" << function >> {
        + falling_detect(): Response
    }
}

' 定义关系
"torch.nn.Module" <|-- TrtModelMMPose : 继承

CommonTools <|-- SmokingFilter : 继承
CommonTools <|-- PhoningFilter : 继承
CommonTools <|-- FallingFilter : 继承

PoseDetector o-- TrtModelMMPose : 组合

Flask -- "phoning_detect" : 关联
Flask -- "smoking_detect" : 关联
Flask -- "falling_detect" : 关联

"phoning_detect" --> PoseDetector : 使用
"phoning_detect" --> PhoningFilter : 创建和使用

"smoking_detect" --> PoseDetector : 使用
"smoking_detect" --> SmokingFilter : 创建和使用

"falling_detect" --> PoseDetector : 使用
"falling_detect" --> FallingFilter : 创建和使用

PoseDetector --> "preprocessing" : 使用
"select_device" --> "torch.nn.Module" : 返回

@enduml
