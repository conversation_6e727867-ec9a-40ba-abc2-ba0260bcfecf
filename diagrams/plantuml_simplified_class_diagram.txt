@startuml MMPose Flask 简化类图

' 设置样式
skinparam classAttributeIconSize 0
skinparam classFontStyle bold
skinparam classBackgroundColor LightBlue
skinparam classBorderColor DarkBlue

' 定义核心类
class Flask {
    + run(host, port)
    + route(rule, methods)
}

class TrtModelMMPose {
    - engine
    - context
    + forward(inputs)
}

class PoseDetector {
    - model: TrtModelMMPose
    + predict(img)
    + postprocessing(outputs, w, h)
}

abstract class CommonTools {
    + calculate_angle(vecA, vecB)
    + calculate_distance(pointA, pointB)
}

class PhoningFilter {
    + filter()
}

class SmokingFilter {
    + filter()
}

class FallingFilter {
    + filter()
}

' 定义关系
CommonTools <|-- PhoningFilter : 继承
CommonTools <|-- SmokingFilter : 继承
CommonTools <|-- FallingFilter : 继承

PoseDetector o-- TrtModelMMPose : 组合

Flask --> PoseDetector : 使用
Flask --> PhoningFilter : 使用
Flask --> SmokingFilter : 使用
Flask --> FallingFilter : 使用

@enduml
