@startuml MMPose Flask 部署图

' 设置样式
skinparam nodeBackgroundColor LightBlue
skinparam nodeBorderColor DarkBlue
skinparam databaseBackgroundColor LightGreen
skinparam databaseBorderColor DarkGreen
skinparam artifactBackgroundColor LightYellow
skinparam artifactBorderColor DarkOrange
skinparam cloudBackgroundColor WhiteSmoke
skinparam cloudBorderColor Gray
skinparam arrowColor DarkBlue

' 定义节点和组件
cloud "互联网" as Internet

node "客户端设备" as ClientDevice {
    artifact "Web浏览器/移动应用" as ClientApp
}

node "服务器" as Server {
    node "Web服务器" as WebServer {
        artifact "Nginx" as Nginx
    }
    
    node "应用服务器" as AppServer {
        artifact "Gunicorn" as Gunicorn
        artifact "Flask应用" as FlaskApp
    }
    
    node "GPU服务器" as GPUServer {
        artifact "CUDA" as CUDA
        artifact "TensorRT" as TensorRT
        artifact "PyTorch" as PyTorch
        artifact "模型文件 (1.engine)" as ModelFile
    }
}

database "日志存储" as LogStorage {
    artifact "应用日志" as AppLogs
    artifact "性能指标" as PerfMetrics
}

node "监控系统" as MonitoringSystem {
    artifact "Prometheus" as Prometheus
    artifact "Grafana" as Grafana
}

' 定义关系
Internet <--> ClientDevice : HTTPS

ClientDevice --> WebServer : HTTPS请求

WebServer --> AppServer : 反向代理
AppServer --> GPUServer : 调用GPU资源

AppServer --> LogStorage : 写入日志
GPUServer --> LogStorage : 记录性能指标

LogStorage --> MonitoringSystem : 提供数据
MonitoringSystem --> Internet : 监控界面访问

' 添加说明
note right of ClientApp
  发送Base64编码的图像
  和位置坐标
end note

note right of FlaskApp
  提供三个API端点:
  - /phoning
  - /smoking
  - /falling
end note

note right of GPUServer
  执行TensorRT优化的
  姿态检测模型
end note

note bottom of LogStorage
  存储请求日志和
  性能监控数据
end note

' 添加部署规格
note bottom of WebServer
  规格:
  - 2 vCPU
  - 4GB RAM
  - 50GB SSD
end note

note bottom of AppServer
  规格:
  - 4 vCPU
  - 8GB RAM
  - 100GB SSD
end note

note bottom of GPUServer
  规格:
  - 8 vCPU
  - 32GB RAM
  - 500GB SSD
  - NVIDIA GPU (至少8GB显存)
end note

@enduml
