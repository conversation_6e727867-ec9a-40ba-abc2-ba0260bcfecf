@startuml MMPose Flask 时序图

' 设置样式
skinparam sequenceArrowThickness 2
skinparam sequenceParticipantBorderThickness 2
skinparam sequenceParticipantBackgroundColor LightBlue
skinparam sequenceParticipantBorderColor DarkBlue
skinparam sequenceLifeLineBorderColor DarkGray
skinparam sequenceLifeLineBackgroundColor LightGray
skinparam sequenceGroupBackgroundColor WhiteSmoke
skinparam sequenceGroupBorderColor Gray
skinparam noteBackgroundColor LightYellow
skinparam noteBorderColor DarkOrange

' 定义参与者
actor "客户端" as Client
participant "Flask服务" as Flask
participant "PoseDetector" as Detector
participant "TrtModelMMPose" as TRTModel
participant "行为过滤器" as Filter

' 时序交互
Client -> Flask: POST请求 (Base64图像 + 位置坐标)
activate Flask

group 图像处理
    Flask -> Flask: 解析JSON请求
    Flask -> Flask: 解码Base64图像
    Flask -> Flask: 转换为NumPy数组
    Flask -> Flask: OpenCV解码图像
    Flask -> Flask: BGR转RGB
    Flask -> Flask: 获取图像尺寸 (h, w)
end

group 姿态检测
    Flask -> Detector: predict(img)
    activate Detector
    
    Detector -> Detector: preprocessing(img)
    note right: 调整大小、标准化、转换为张量
    
    Detector -> TRTModel: forward(dict(input=img0))
    activate TRTModel
    
    TRTModel -> TRTModel: 检查输入形状
    TRTModel -> TRTModel: 设置绑定形状
    TRTModel -> TRTModel: 创建输出张量
    TRTModel -> TRTModel: 执行异步推理
    
    TRTModel --> Detector: 返回热力图 (1×17×64×48)
    deactivate TRTModel
    
    Detector -> Detector: postprocessing(outputs, w, h)
    note right
        1. 提取每个关键点的热力图
        2. 找到热力图最大值位置
        3. 计算关键点坐标和置信度
    end note
    
    Detector --> Flask: 返回关键点坐标和置信度 (k, s)
    deactivate Detector
end

group 行为判断
    alt 打电话检测
        Flask -> Filter: 创建PhoningFilter(k, s, center_point)
        activate Filter
        Flask -> Filter: filter()
        Filter -> Filter: 检查关键点置信度
        Filter -> Filter: 判断电话位置
        Filter -> Filter: 计算手臂角度
        Filter -> Filter: 判断手腕位置
        Filter --> Flask: 返回是否打电话 (布尔值)
        deactivate Filter
    else 吸烟检测
        Flask -> Filter: 创建SmokingFilter(k, s, center_point)
        activate Filter
        Flask -> Filter: filter()
        Filter -> Filter: 检查关键点置信度
        Filter -> Filter: 判断香烟位置
        Filter -> Filter: 计算手臂角度
        Filter -> Filter: 判断手腕位置
        Filter --> Flask: 返回是否吸烟 (布尔值)
        deactivate Filter
    else 摔倒检测
        Flask -> Filter: 创建FallingFilter(k, s)
        activate Filter
        Flask -> Filter: filter()
        Filter -> Filter: 检查关键点置信度
        Filter -> Filter: 计算身体角度
        Filter -> Filter: 计算身体比例
        Filter -> Filter: 判断是否摔倒
        Filter --> Flask: 返回是否摔倒 (布尔值)
        deactivate Filter
    end
end

Flask -> Flask: 构建JSON响应
Flask --> Client: 返回检测结果 (JSON)
deactivate Flask

@enduml
