@startuml MMPose Flask 组件图

' 设置样式
skinparam componentBackgroundColor LightBlue
skinparam componentBorderColor DarkBlue
skinparam interfaceBackgroundColor White
skinparam interfaceBorderColor DarkGray
skinparam arrowColor DarkBlue

' 定义组件
package "客户端" {
    [客户端应用] as Client
}

package "服务器" {
    package "Web服务层" {
        [Flask应用] as Flask
        [HTTP请求处理] as HTTPHandler
        [JSON解析/生成] as JSONProcessor
    }
    
    package "业务逻辑层" {
        [姿态检测器] as PoseDetector
        [行为过滤器] as ActionFilters
    }
    
    package "模型层" {
        [TensorRT引擎] as TRTEngine
        [模型文件] as ModelFile
    }
    
    package "工具层" {
        [图像处理] as ImageProcessor
        [几何计算] as GeometryTools
    }
}

' 定义接口
interface "HTTP API" as API
interface "姿态检测接口" as PoseAPI
interface "行为判断接口" as ActionAPI
interface "TensorRT接口" as TRTAPI
interface "图像处理接口" as ImageAPI

' 定义关系
Client -- API
API -- HTTPHandler

HTTPHandler --> JSONProcessor : 使用
HTTPHandler --> PoseAPI : 调用

PoseAPI -- PoseDetector
PoseDetector --> ActionAPI : 提供数据
ActionAPI -- ActionFilters

PoseDetector --> TRTAPI : 调用
TRTAPI -- TRTEngine
TRTEngine --> ModelFile : 加载

PoseDetector --> ImageAPI : 调用
ImageAPI -- ImageProcessor

ActionFilters --> GeometryTools : 使用

' 添加说明
note right of API
  提供三个端点:
  - /phoning
  - /smoking
  - /falling
end note

note bottom of ActionFilters
  包含三种过滤器:
  - PhoningFilter
  - SmokingFilter
  - FallingFilter
end note

note bottom of ModelFile
  TensorRT优化的模型:
  - 1.engine
end note

@enduml
