@startuml MMPose Flask 数据流程图

' 设置样式
skinparam activityBackgroundColor LightBlue
skinparam activityBorderColor DarkBlue
skinparam arrowColor DarkBlue
skinparam swimlaneBorderColor Gray

' 定义泳道
|客户端|
|Flask服务|
|图像处理|
|姿态检测|
|行为分析|

' 数据流程
|客户端|
start
:发送HTTP POST请求;
note right: 包含Base64编码的图像和位置坐标

|Flask服务|
:接收请求数据;
:解析JSON数据;
:提取Base64图像和位置坐标;

|图像处理|
:Base64解码;
:转换为NumPy数组;
:OpenCV解码图像;
:BGR转RGB;
:获取图像尺寸;

|姿态检测|
:图像预处理;
note right
  1. 转换为PIL图像
  2. 调整大小为256×192
  3. 转换为张量
  4. 标准化
  5. 添加批次维度
  6. 移动到GPU
end note
:TensorRT模型推理;
note right: 输出热力图(1×17×64×48)
:热力图后处理;
note right
  1. 找到每个热力图的最大值位置
  2. 记录最大值作为置信度
  3. 将热力图坐标映射回原始图像
end note
:生成关键点坐标和置信度;

|行为分析|
:创建行为过滤器;
note right: 根据API端点选择不同的过滤器
:检查关键点置信度;
:判断物体位置;
:计算身体部位角度;
:应用行为判断规则;
:生成行为判断结果;

|Flask服务|
:构建JSON响应;
:返回HTTP响应;

|客户端|
:接收并处理响应;
stop

@enduml
