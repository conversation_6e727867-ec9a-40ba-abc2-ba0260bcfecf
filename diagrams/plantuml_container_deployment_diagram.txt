@startuml MMPose Flask 容器化部署图

' 设置样式
skinparam nodeBackgroundColor LightBlue
skinparam nodeBorderColor DarkBlue
skinparam artifactBackgroundColor LightYellow
skinparam artifactBorderColor DarkOrange
skinparam componentBackgroundColor LightGreen
skinparam componentBorderColor DarkGreen
skinparam databaseBackgroundColor WhiteSmoke
skinparam databaseBorderColor Gray
skinparam arrowColor DarkBlue

' 定义节点和组件
node "Kubernetes集群" as K8s {
    node "Ingress控制器" as Ingress {
        artifact "Nginx Ingress" as NginxIngress
    }
    
    node "应用Pod" as AppPod {
        component "Flask容器" as FlaskContainer {
            artifact "Flask应用" as FlaskApp
            artifact "Gunicorn" as Gunicorn
        }
    }
    
    node "GPU Pod" as GPUPod {
        component "TensorRT容器" as TRTContainer {
            artifact "TensorRT" as TensorRT
            artifact "PyTorch" as PyTorch
            artifact "CUDA" as CUDA
        }
        
        database "模型存储" as ModelStorage {
            artifact "1.engine" as ModelFile
        }
    }
    
    node "监控Pod" as MonitoringPod {
        component "Prometheus容器" as PrometheusContainer {
            artifact "Prometheus" as Prometheus
        }
        
        component "Grafana容器" as GrafanaContainer {
            artifact "Grafana" as Grafana
        }
    }
    
    database "持久化存储" as PersistentStorage {
        artifact "日志" as Logs
        artifact "指标数据" as Metrics
    }
}

node "客户端" as Client {
    artifact "Web应用/移动应用" as ClientApp
}

' 定义关系
Client --> Ingress : HTTPS请求
Ingress --> AppPod : 路由请求
AppPod --> GPUPod : gRPC调用
AppPod --> PersistentStorage : 写入日志
GPUPod --> ModelStorage : 加载模型
GPUPod --> PersistentStorage : 记录性能指标
MonitoringPod --> PersistentStorage : 读取指标
Client --> MonitoringPod : 访问监控界面

' 添加说明
note right of FlaskApp
  提供三个API端点:
  - /phoning
  - /smoking
  - /falling
end note

note right of TRTContainer
  使用NVIDIA GPU加速
  姿态检测和行为分析
end note

note bottom of ModelStorage
  存储TensorRT优化的模型
  支持版本控制和回滚
end note

note bottom of PersistentStorage
  使用云存储服务
  确保数据持久性
end note

' 添加部署配置
note top of AppPod
  部署配置:
  - 自动扩缩容 (2-10个副本)
  - 资源限制: 2 CPU, 4GB RAM
  - 健康检查: /health
  - 存活探针: HTTP GET /
end note

note top of GPUPod
  部署配置:
  - 固定副本数 (1-2个)
  - 资源限制: 4 CPU, 16GB RAM, 1 GPU
  - GPU亲和性调度
  - 预热启动脚本
end note

note top of MonitoringPod
  部署配置:
  - 单副本部署
  - 资源限制: 1 CPU, 2GB RAM
  - 持久化存储卷
end note

@enduml
