import numpy as np


# 关键点序号
# {
#     "nose": 0,
#     "left_eye": 1,
#     "right_eye": 2,
#     "left_ear": 3,
#     "right_ear": 4,
#     "left_shoulder": 5,
#     "right_shoulder": 6,
#     "left_elbow": 7,
#     "right_elbow": 8,
#     "left_wrist": 9,
#     "right_wrist": 10,
#     "left_hip": 11,
#     "right_hip": 12,
#     "left_knee": 13,
#     "right_knee": 14,
#     "left_ankle": 15,
#     "right_ankle": 16
# }


class CommonTools:
    def __init__(self):
        pass

    def calculate_angle(self, vecA, vecB):
        """
        Calculate the angle between two vectors
        vecA and vecB are the two numpy vectors
        """
        # 计算点积  
        dot_product = np.dot(vecA, vecB)  
        
        # 计算模长  
        norm_A = np.linalg.norm(vecA)  
        norm_B = np.linalg.norm(vecB)  
        
        # 计算夹角余弦值  
        cos_theta = dot_product / (norm_A * norm_B)  
        
        # 计算夹角，注意arccos返回的是弧度，需要转换为度  
        theta = np.degrees(np.arccos(cos_theta))  
        
        return theta 
    
    def calculate_distance(self, pointA, pointB):
        """
        Calculate the distance between two points
        """
        return np.linalg.norm(pointA - pointB)
    
    def judge_position(self):
        pass


class SmokingFilter(CommonTools):
    def __init__(self, keypoints, scores:list, center_point):
        """
        :param keypoints:
        :param scores:
        :param center_point: 香烟中心点
        """
        super().__init__()
        self.smoking_threshold = 0.2
        self.keypoints = np.array(keypoints)
        self.scores = scores
        self.center_point = np.array(center_point)

    def set_score_thresh(self, thresh):
        self.smoking_threshold = thresh

    def filter(self, angle_thresh=60):
        # 检查关键点置信度
        count = sum(1 for i in self.scores if i > self.smoking_threshold)
        if count > 9:  # 9个关键点置信度大于阈值
            # 香烟中心和嘴部位置判断
            cig_nose_dis = self.calculate_distance(self.center_point, self.keypoints[0])
            eyes_dis = self.calculate_distance(self.keypoints[1], self.keypoints[2])

            # 大于双眼距离，或者香烟在鼻子上面，则不报警
            if cig_nose_dis > 3 * eyes_dis or self.center_point[1] < self.keypoints[0][1]:
                return False

            # 计算向量夹角
            left_elbow_wrist_vec = self.keypoints[7] - self.keypoints[9]
            right_elbow_wrist_vec = self.keypoints[8] - self.keypoints[10]
            left_elbow_shoulder_vec = self.keypoints[7] - self.keypoints[5]
            right_elbow_shoulder_vec = self.keypoints[8] - self.keypoints[6]

            left_angle = self.calculate_angle(left_elbow_wrist_vec, left_elbow_shoulder_vec)
            right_angle = self.calculate_angle(right_elbow_wrist_vec, right_elbow_shoulder_vec)

            # 抽烟时，有一只手的关节夹角小于60度，并且wrist-ear不是很小, wrist-nose不是很大
            if left_angle < angle_thresh and left_angle < right_angle:
                ear_wrist_dis = self.calculate_distance(self.keypoints[3], self.keypoints[9])
                wrist_nose_dis = self.calculate_distance(self.keypoints[0], self.keypoints[9])
                wrist_elbow_dis = self.calculate_distance(self.keypoints[7], self.keypoints[9])
                wrist_match = self.keypoints[9]
                elbow_match = self.keypoints[7]

            elif right_angle < angle_thresh and right_angle < left_angle:
                ear_wrist_dis = self.calculate_distance(self.keypoints[4], self.keypoints[10])
                wrist_nose_dis = self.calculate_distance(self.keypoints[0], self.keypoints[10])
                wrist_elbow_dis = self.calculate_distance(self.keypoints[8], self.keypoints[10])
                wrist_match = self.keypoints[10]
                elbow_match = self.keypoints[8]
            
            else:
                return False
            
            # if ear_wrist_dis > eyes_dis and wrist_nose_dis < 0.9*wrist_elbow_dis:
            # 手腕y坐标在手肘和鼻子之间
            if elbow_match[1] > wrist_match[1] > self.keypoints[0][1]:
                return True
            else:
                return False
        
        else:
            print("Not enough threshed keypoints")
            return False
        

class PhoningFilter(CommonTools):
    def __init__(self, keypoints, scores:list, center_point):
        super().__init__()
        self.phoning_threshold = 0.2
        self.keypoints = np.array(keypoints)
        self.scores = scores
        self.center_point = np.array(center_point)

    def set_score_thresh(self, thresh):
        self.phoning_threshold = thresh

    def filter(self, angle_thresh=60):
        # 检查关键点置信度
        count = sum(1 for i in self.scores if i > self.phoning_threshold)
        if count > 9:
            # 电话中心和耳朵位置判断
            ears_center = (self.keypoints[3] + self.keypoints[4]) / 2
            phone_ear_dis = self.calculate_distance(self.center_point, ears_center)
            shoulder_dis = self.calculate_distance(self.keypoints[6], self.keypoints[5])
            # 电话距离耳朵远，不报警
            if phone_ear_dis > shoulder_dis * 0.8:
                return False
            # 计算向量夹角
            left_elbow_wrist_vec = self.keypoints[7] - self.keypoints[9]
            right_elbow_wrist_vec = self.keypoints[8] - self.keypoints[10]
            left_elbow_shoulder_vec = self.keypoints[7] - self.keypoints[5]
            right_elbow_shoulder_vec = self.keypoints[8] - self.keypoints[6]

            left_angle = self.calculate_angle(left_elbow_wrist_vec, left_elbow_shoulder_vec)
            right_angle = self.calculate_angle(right_elbow_wrist_vec, right_elbow_shoulder_vec)

            # 打电话时，有一只手的关节夹角小于60度，并且wrist-ear很小, wrist-nose较大
            if left_angle < angle_thresh and left_angle < right_angle:
                ear_wrist_dis = self.calculate_distance(self.keypoints[3], self.keypoints[9])
                wrist_nose_dis = self.calculate_distance(self.keypoints[0], self.keypoints[9])
                wrist_elbow_dis = self.calculate_distance(self.keypoints[7], self.keypoints[9])

            elif right_angle < angle_thresh and right_angle < left_angle:
                ear_wrist_dis = self.calculate_distance(self.keypoints[4], self.keypoints[10])
                wrist_nose_dis = self.calculate_distance(self.keypoints[0], self.keypoints[10])
                wrist_elbow_dis = self.calculate_distance(self.keypoints[8], self.keypoints[10])
            else:
                return False
            
            if 0.5 * wrist_elbow_dis < ear_wrist_dis < wrist_nose_dis:
                return True 
            else:
                return False

        else:
            return False


class FallingFilter(CommonTools):
    def __init__(self, keypoints, scores:list):
        super().__init__()
        self.falling_threshold = 0.2
        self.keypoints = np.array(keypoints)
        self.scores = scores

    def set_score_thresh(self, thresh):
        self.falling_threshold = thresh

    def filter(self, angle_thresh=60):
        # 检查关键点置信度, 超过9个关键点置信度不够，就不认为是人
        count = sum(1 for i in self.scores if i > self.falling_threshold)
        if count < 9:
            return False

        horizontal_vec = np.array([1, 0])

        # 计算四肢与水平线夹角
        left_hip, right_hip = self.keypoints[11], self.keypoints[12]
        left_shoulder, right_shoulder = self.keypoints[5], self.keypoints[6]
        left_ankle, right_ankle = self.keypoints[15], self.keypoints[16]
        left_ear, right_ear = self.keypoints[3], self.keypoints[4]

        # 中心点，耳部中心点为C1, 臀部C0，脚部C2
        C0 = (left_hip + right_hip) / 2
        C1 = (left_ear + right_ear) / 2
        C2 = (left_ankle + right_ankle) / 2

        # 上下身体比例
        p = self.calculate_distance(C0, C1) / self.calculate_distance(C0, C2)
        # 特征向量
        D1 = C1 - C0
        D2 = C0 - C2

        # 与水平向量夹角
        theta1 = self.calculate_angle(D1, horizontal_vec)
        theta1 = theta1 if theta1 < 90 else 180 - theta1
        theta2 = self.calculate_angle(D2, horizontal_vec)
        theta2 = theta2 if theta2 < 90 else 180 - theta2

        print(theta1, theta2)
        print('p: ', p)

        # 坐下活动：p 1.5~2.2，theta1较大，大于60，theta2也比较大
        # 下蹲活动：p 2.5~3.5， 70<theta1<90，theta2 90
        if theta1 < 45 and theta2 < 45 and 0.9 < p < 1.6:
            return True

        return False
