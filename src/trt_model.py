import cv2
from Tensorrt import *
from torchvision import transforms

"""
1.加载TensorRT模型（通过权重文件，例如1.engine）。
2.对输入图像进行预处理，调整为模型所需的格式。
3.使用TensorRT模型进行推理，生成关键点的热力图。
4.对热力图进行后处理，转换为实际的关键点坐标和置信度分数。
5.可视化检测结果，将关键点绘制在原始图像上并保存。
"""

def preprocessing(ori_img):
    img_transform = transforms.Compose(
        [
            transforms.ToPILImage(),  # 需要opencv直接读取输入
            transforms.Resize((256, 192)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

    img = img_transform(ori_img)

    img = torch.unsqueeze(img, dim=0).cuda()

    return img


class PoseDetector(object):
    def __init__(self, weights, device):
        self.input_shape = [256, 192]
        self.device = device
        self.weights = weights
        self.model = TrtModelMMPose(self.weights, ['output'])

    def _warm_up(self):
        """用于模型正式启动前的热身
        通过运行一个随机输入张量初始化模型，减少首次推理的延迟。
        Args:
            size (int, optional): 用于热身的tensor的尺寸. Defaults to 640.
        """
        self.model(dict(input=torch.randn(1, 3, self.input_shape[0], self.input_shape[1]).to(DEVICE)))

    def predict(self, img):
        img0 = preprocessing(img)
        pred = self.model(dict(input=img0))
        return pred
    
    def postprocessing(self, outputs, ori_img_w, ori_img_h):
        """得到热力图后的后处理操作

        Args:
            outputs (tensor): 1x17x48x64
            ori_img_w (int): 原始图片的宽
            ori_img_w (int): 原始图片的高
        Return:
            keypoints: 二维list, 包含17个关键点坐标, [[x1,y1], [x2,y2], ...]
            scores: 一维list, 对应的17个关键点的分数
        """
        keypoints = []
        scores = []
        w, h = ori_img_w, ori_img_h
        # print('%s x %s'%(w, h))
        for i in range(17):
            heatmap = outputs[0, i, :, :]
            heatmap_flatten = heatmap.view(-1)
            max_value, max_index = torch.max(heatmap_flatten, 0)
            scores.append(max_value.tolist())
            heatmap_loc = [max_index.cpu().numpy() // heatmap.size(1), max_index.cpu().numpy() % heatmap.size(1)]
            keypoints_x = heatmap_loc[1] * (256 / 64) * (w / 192)
            keypoints_y = heatmap_loc[0] * (192 / 48) * (h / 256)
            keypoints.append([keypoints_x, keypoints_y])

        return keypoints, scores
    

def detect(img):
    h, w, _ = img.shape
    # w, h = img.size
    pose_detector = PoseDetector()
    outputs = pose_detector.predict(img)
    keypoints, scores = pose_detector.postprocessing(outputs, w, h)
    keypoints = [[int(num) for num in sub] for sub in keypoints]

    return keypoints, scores


if __name__ == '__main__':
    import time

    DEVICE = select_device('0')
    WEIGHT = '1.engine'

    img = cv2.imread(r'F:\alarm_data_live\tmp\cy_TP2.jpg')
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    pose_detector = PoseDetector(WEIGHT, DEVICE)
    for i in range(1):
        start = time.time()
        h, w, _ = img.shape
        outputs = pose_detector.predict(img_rgb)
        end = time.time()
        k, s = pose_detector.postprocessing(outputs, w, h)
        print((end-start) * 1000, 'ms')
    print(k)
    print(s)

    for i,p in enumerate(k):
        if s[i] > 0.2:
            cv2.circle(img, (int(p[0]), int(p[1])), 3, (0, 255, 0), -1)
        cv2.imwrite('keypoints.jpg', img)
