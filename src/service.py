import json
import cv2
import base64
import time
import numpy as np
from flask import Flask, jsonify, request

from action_filters import *
from trt_model import PoseDetector
from Tensorrt import *


DEVICE = select_device('0')
WEIGHTS = '1.engine'
app = Flask(__name__)

pose_detector = PoseDetector(WEIGHTS, DEVICE)


@app.route('/phoning', methods=['POST'])
def phoning_detect():
    start = time.time()

    data = json.loads(request.data)
    img_base64 = data['image']
    pointx = int(data['boxLocX'])
    pointy = int(data['boxLocY'])
    center_point = [pointx, pointy]
    img_binary = base64.b64decode(img_base64)
    nparr = np.frombuffer(img_binary, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

    h, w, _ = img.shape
    outs = pose_detector.predict(img)
    k, s = pose_detector.postprocessing(outs, w, h)

    pfilter = PhoningFilter(k, s, center_point)
    is_phoning = pfilter.filter()

    end = time.time()
    print('phoning filter time cost: ', end - start)

    return jsonify({'result': is_phoning})


@app.route('/smoking', methods=['POST'])
def smoking_detect():
    start = time.time()

    data = json.loads(request.data)
    img_base64 = data['image']
    pointx = int(data['boxLocX'])
    pointy = int(data['boxLocY'])
    center_point = [pointx, pointy]
    img_binary = base64.b64decode(img_base64)
    nparr = np.frombuffer(img_binary, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

    h, w, _ = img.shape
    outs = pose_detector.predict(img)
    k, s = pose_detector.postprocessing(outs, w, h)

    pfilter = SmokingFilter(k, s, center_point)
    is_smoking = pfilter.filter()

    end = time.time()
    print('smoke filter time cost: ', end - start)

    return jsonify({'result': is_smoking})


@app.route('/falling', methods=['POST'])
def falling_detect():
    start = time.time()

    data = json.loads(request.data)
    img_base64 = data['image']
    img_binary = base64.b64decode(img_base64)
    nparr = np.frombuffer(img_binary, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

    h, w, _ = img.shape
    outs = pose_detector.predict(img)
    k, s = pose_detector.postprocessing(outs, w, h)
    print(k)

    pfilter = FallingFilter(k, s)
    is_falling = pfilter.filter()

    end = time.time()
    print('falling filter time cost: ', end - start)

    return jsonify({'result': is_falling})


app.run(host='0.0.0.0', port=20249)
