import numpy as np


# 关键点序号
# {
#     "nose": 0,
#     "left_eye": 1,
#     "right_eye": 2,
#     "left_ear": 3,
#     "right_ear": 4,
#     "left_shoulder": 5,
#     "right_shoulder": 6,
#     "left_elbow": 7,
#     "right_elbow": 8,
#     "left_wrist": 9,
#     "right_wrist": 10,
#     "left_hip": 11,
#     "right_hip": 12,
#     "left_knee": 13,
#     "right_knee": 14,
#     "left_ankle": 15,
#     "right_ankle": 16
# }


class CommonTools:
    def __init__(self):
        pass

    def calculate_angle(self, vecA, vecB):
        """
        Calculate the angle between two vectors
        """
        # 计算点积  
        dot_product = np.dot(vecA, vecB)  
        
        # 计算模长  
        norm_A = np.linalg.norm(vecA)  
        norm_B = np.linalg.norm(vecB)  
        
        # 计算夹角余弦值  
        cos_theta = dot_product / (norm_A * norm_B)  
        
        # 计算夹角，注意arccos返回的是弧度，需要转换为度  
        theta = np.degrees(np.arccos(cos_theta))  
        
        return theta 
    
    def calculate_distance(self, pointA, pointB):
        """
        Calculate the distance between two points
        """
        return np.linalg.norm(pointA - pointB)
    
    def judge_position(self):
        pass


class SmokingFilter(CommonTools):
    def __init__(self, keypoints, scores:list):
        super().__init__()
        self.smoking_threshold = 0.2
        self.keypoints = np.array(keypoints)
        self.scores = scores

    def set_score_thresh(self, thresh):
        self.smoking_threshold = thresh

    def filter(self, angle_thresh=60):
        # 检查关键点置信度
        count = sum(1 for i in self.scores if i > self.smoking_threshold)
        if count > 9:
            # 计算向量夹角
            left_elbow_wrist_vec = self.keypoints[7] - self.keypoints[9]
            right_elbow_wrist_vec = self.keypoints[8] - self.keypoints[10]
            left_elbow_shoulder_vec = self.keypoints[7] - self.keypoints[5]
            right_elbow_shoulder_vec = self.keypoints[8] - self.keypoints[6]

            left_angle = self.calculate_angle(left_elbow_wrist_vec, left_elbow_shoulder_vec)
            right_angle = self.calculate_angle(right_elbow_wrist_vec, right_elbow_shoulder_vec)

            # 抽烟时，有一只手的关节夹角小于60度，并且wrist-ear不是很小, wrist-nose不是很大
            if left_angle < angle_thresh and left_angle < right_angle:
                ear_wrist_dis = self.calculate_distance(self.keypoints[3], self.keypoints[9])
                wrist_nose_dis = self.calculate_distance(self.keypoints[0], self.keypoints[9])
                wrist_elbow_dis = self.calculate_distance(self.keypoints[7], self.keypoints[9])

            elif right_angle < angle_thresh and right_angle < left_angle:
                ear_wrist_dis = self.calculate_distance(self.keypoints[4], self.keypoints[10])
                wrist_nose_dis = self.calculate_distance(self.keypoints[0], self.keypoints[10])
                wrist_elbow_dis = self.calculate_distance(self.keypoints[8], self.keypoints[10])
            
            else:
                return False
            
            # 计算其它关键点距离，对比参照
            eyes_dis = self.calculate_distance(self.keypoints[1], self.keypoints[2])
            if ear_wrist_dis > eyes_dis and wrist_nose_dis < 0.9*wrist_elbow_dis:
                return True
            else:
                return False
        
        else:
            print("Not enough threshed keypoints")
            return False
        

class PhoningFilter(CommonTools):
    def __init__(self, keypoints, scores:list):
        super().__init__()
        self.phoning_threshold = 0.2
        self.keypoints = np.array(keypoints)
        self.scores = scores

    def set_score_thresh(self, thresh):
        self.phoning_threshold = thresh

    def filter(self, angle_thresh=60):
        # 检查关键点置信度
        count = sum(1 for i in self.scores if i > self.phoning_threshold)
        if count > 9:
            # 计算向量夹角
            left_elbow_wrist_vec = self.keypoints[7] - self.keypoints[9]
            right_elbow_wrist_vec = self.keypoints[8] - self.keypoints[10]
            left_elbow_shoulder_vec = self.keypoints[7] - self.keypoints[5]
            right_elbow_shoulder_vec = self.keypoints[8] - self.keypoints[6]

            left_angle = self.calculate_angle(left_elbow_wrist_vec, left_elbow_shoulder_vec)
            right_angle = self.calculate_angle(right_elbow_wrist_vec, right_elbow_shoulder_vec)

            # 打电话时，有一只手的关节夹角小于60度，并且wrist-ear很小, wrist-nose较大
            if left_angle < angle_thresh and left_angle < right_angle:
                ear_wrist_dis = self.calculate_distance(self.keypoints[3], self.keypoints[9])
                wrist_nose_dis = self.calculate_distance(self.keypoints[0], self.keypoints[9])
                wrist_elbow_dis = self.calculate_distance(self.keypoints[7], self.keypoints[9])

            elif right_angle < angle_thresh and right_angle < left_angle:
                ear_wrist_dis = self.calculate_distance(self.keypoints[4], self.keypoints[10])
                wrist_nose_dis = self.calculate_distance(self.keypoints[0], self.keypoints[10])
                wrist_elbow_dis = self.calculate_distance(self.keypoints[8], self.keypoints[10])
            else:
                return False
            
            if 0.5 * wrist_elbow_dis < ear_wrist_dis < wrist_nose_dis:
                return True 
            else:
                return False

        else:
            return False