# .gitignore

# Runtime directories
logs/
temp/
!logs/README.md
!temp/README.md

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
*.egg-info/
dist/
build/
.pytest_cache/

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
.DS_Store

# Model files (large)
# models/*.engine  # Uncomment if you don't want to track model files

# Docker
.dockerignore
docker-compose.override.yml

# Environment variables
.env
.env.local
.env.production

# Backup files
*.bak
*.backup
backup/

# OS generated files
Thumbs.db