# Python TensorRT 推理 SDK 技术文档

## 目录
1. [概述](#概述)
2. [安装与环境配置](#安装与环境配置)
3. [核心概念](#核心概念)
4. [基础API使用](#基础api使用)
5. [模型转换与优化](#模型转换与优化)
6. [推理引擎使用](#推理引擎使用)
7. [性能优化](#性能优化)
8. [完整代码示例](#完整代码示例)
9. [故障排除](#故障排除)
10. [最佳实践](#最佳实践)

---

## 概述

TensorRT 是 NVIDIA 开发的高性能深度学习推理优化器和运行时库。Python TensorRT SDK 提供了完整的 Python API，用于模型优化、推理引擎构建和高效推理执行。

### 主要特性
- **模型优化**: 自动进行层融合、精度校准、内核调优
- **多精度支持**: FP32、FP16、INT8 量化推理
- **动态形状**: 支持可变输入尺寸
- **流水线并行**: 异步推理和 CUDA 流管理
- **跨平台**: 支持 x86、ARM、Jetson 等平台

---

## 安装与环境配置

### 系统要求
```bash
# NVIDIA GPU 驱动要求
CUDA >= 11.0
Driver >= 450.80.02 (Linux) / 452.39 (Windows)

# Python 版本
Python >= 3.6
```

### 安装方法

#### 方法1: pip安装 (推荐)
```bash
pip install nvidia-tensorrt
```

#### 方法2: 从 NVIDIA 官方下载
```bash
# 下载 TensorRT tar包
tar -xzvf TensorRT-8.x.x.x.Linux.x86_64-gnu.cuda-11.x.cudnn8.x.tar.gz
cd TensorRT-8.x.x.x

# 安装 Python wheel
cd python
pip install tensorrt-8.x.x.x-cp38-none-linux_x86_64.whl
```

### 环境验证
```python
import tensorrt as trt

print(f"TensorRT version: {trt.__version__}")
print(f"Available GPU devices: {trt.Builder(trt.Logger()).create_builder_config().get_memory_pool_limit(trt.MemoryPoolType.WORKSPACE)}")
```

---

## 核心概念

### 1. Logger (日志系统)
```python
import tensorrt as trt

# 创建 Logger
logger = trt.Logger(trt.Logger.WARNING)  # 可选: VERBOSE, INFO, WARNING, ERROR

# 自定义 Logger
class CustomLogger(trt.ILogger):
    def log(self, severity, msg):
        print(f"[TRT {severity}]: {msg}")

custom_logger = CustomLogger()
```

### 2. Builder (构建器)
```python
# 创建 Builder
builder = trt.Builder(logger)
config = builder.create_builder_config()

# 配置优化参数
config.max_workspace_size = 1 << 30  # 1GB
config.set_flag(trt.BuilderFlag.FP16)  # 启用FP16精度
```

### 3. Network Definition (网络定义)
```python
# 显式网络定义
network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))

# 从ONNX模型解析
parser = trt.OnnxParser(network, logger)
with open("model.onnx", "rb") as f:
    parser.parse(f.read())
```

### 4. Engine (推理引擎)
```python
# 构建引擎
engine = builder.build_engine(network, config)

# 序列化引擎
with open("model.engine", "wb") as f:
    f.write(engine.serialize())

# 反序列化引擎
with open("model.engine", "rb") as f:
    engine = trt.Runtime(logger).deserialize_cuda_engine(f.read())
```

---

## 基础API使用

### 完整的模型转换流程

```python
import tensorrt as trt
import pycuda.driver as cuda
import pycuda.autoinit
import numpy as np

def build_engine_from_onnx(onnx_file_path, engine_file_path, max_batch_size=1):
    """从ONNX文件构建TensorRT引擎"""
    
    # 1. 创建基础组件
    logger = trt.Logger(trt.Logger.WARNING)
    builder = trt.Builder(logger)
    network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
    parser = trt.OnnxParser(network, logger)
    
    # 2. 解析ONNX模型
    with open(onnx_file_path, 'rb') as model:
        if not parser.parse(model.read()):
            print('Failed to parse ONNX model')
            for error in range(parser.num_errors):
                print(parser.get_error(error))
            return None
    
    # 3. 配置优化参数
    config = builder.create_builder_config()
    config.max_workspace_size = 1 << 30  # 1GB
    
    # 启用精度优化
    if builder.platform_has_fast_fp16:
        config.set_flag(trt.BuilderFlag.FP16)
        print("FP16 mode enabled")
    
    # 4. 构建引擎
    print("Building TensorRT engine...")
    engine = builder.build_engine(network, config)
    
    if engine is None:
        print("Failed to build engine")
        return None
    
    # 5. 保存引擎
    with open(engine_file_path, "wb") as f:
        f.write(engine.serialize())
    
    print(f"Engine saved to {engine_file_path}")
    return engine

# 使用示例
engine = build_engine_from_onnx("model.onnx", "model.engine")
```

### 推理执行

```python
class TensorRTInference:
    def __init__(self, engine_path):
        self.logger = trt.Logger(trt.Logger.WARNING)
        
        # 加载引擎
        with open(engine_path, "rb") as f:
            self.engine = trt.Runtime(self.logger).deserialize_cuda_engine(f.read())
        
        self.context = self.engine.create_execution_context()
        
        # 分配内存
        self.allocate_buffers()
    
    def allocate_buffers(self):
        """分配GPU和CPU内存缓冲区"""
        self.inputs = []
        self.outputs = []
        self.bindings = []
        
        for binding in self.engine:
            size = trt.volume(self.engine.get_binding_shape(binding)) * self.engine.max_batch_size
            dtype = trt.nptype(self.engine.get_binding_dtype(binding))
            
            # 分配CPU内存
            host_mem = cuda.pagelocked_empty(size, dtype)
            
            # 分配GPU内存
            device_mem = cuda.mem_alloc(host_mem.nbytes)
            
            self.bindings.append(int(device_mem))
            
            if self.engine.binding_is_input(binding):
                self.inputs.append({'host': host_mem, 'device': device_mem})
            else:
                self.outputs.append({'host': host_mem, 'device': device_mem})
    
    def infer(self, input_data):
        """执行推理"""
        # 复制输入数据到GPU
        np.copyto(self.inputs[0]['host'], input_data.ravel())
        cuda.memcpy_htod(self.inputs[0]['device'], self.inputs[0]['host'])
        
        # 执行推理
        self.context.execute(batch_size=1, bindings=self.bindings)
        
        # 复制输出数据到CPU
        cuda.memcpy_dtoh(self.outputs[0]['host'], self.outputs[0]['device'])
        
        return self.outputs[0]['host']

# 使用示例
inference = TensorRTInference("model.engine")
result = inference.infer(input_image)
```

---

## 模型转换与优化

### 1. ONNX 到 TensorRT 转换

```python
def convert_onnx_to_tensorrt(onnx_path, trt_path, precision='fp16'):
    """
    将ONNX模型转换为TensorRT引擎
    
    Args:
        onnx_path: ONNX模型路径
        trt_path: 输出的TensorRT引擎路径
        precision: 精度模式 ('fp32', 'fp16', 'int8')
    """
    logger = trt.Logger(trt.Logger.INFO)
    builder = trt.Builder(logger)
    
    # 创建网络
    network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
    parser = trt.OnnxParser(network, logger)
    
    # 解析ONNX
    with open(onnx_path, 'rb') as model:
        if not parser.parse(model.read()):
            for error in range(parser.num_errors):
                print(parser.get_error(error))
            return False
    
    # 配置构建参数
    config = builder.create_builder_config()
    config.max_workspace_size = 2 << 30  # 2GB
    
    # 设置精度
    if precision == 'fp16' and builder.platform_has_fast_fp16:
        config.set_flag(trt.BuilderFlag.FP16)
        print("Using FP16 precision")
    elif precision == 'int8' and builder.platform_has_fast_int8:
        config.set_flag(trt.BuilderFlag.INT8)
        # 需要设置INT8校准器
        print("Using INT8 precision")
    else:
        print("Using FP32 precision")
    
    # 构建引擎
    engine = builder.build_engine(network, config)
    
    if engine:
        with open(trt_path, 'wb') as f:
            f.write(engine.serialize())
        print(f"Engine saved to {trt_path}")
        return True
    
    return False
```

### 2. 动态形状支持

```python
def build_engine_with_dynamic_shapes(onnx_path, engine_path):
    """构建支持动态形状的TensorRT引擎"""
    logger = trt.Logger(trt.Logger.WARNING)
    builder = trt.Builder(logger)
    network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
    parser = trt.OnnxParser(network, logger)
    
    # 解析ONNX
    with open(onnx_path, 'rb') as model:
        parser.parse(model.read())
    
    config = builder.create_builder_config()
    config.max_workspace_size = 1 << 30
    
    # 设置动态形状
    input_tensor = network.get_input(0)
    profile = builder.create_optimization_profile()
    
    # 定义输入形状范围 (min, optimal, max)
    profile.set_shape(input_tensor.name, 
                     min=(1, 3, 224, 224),     # 最小形状
                     opt=(4, 3, 224, 224),     # 最优形状
                     max=(8, 3, 224, 224))     # 最大形状
    
    config.add_optimization_profile(profile)
    
    # 构建引擎
    engine = builder.build_engine(network, config)
    
    with open(engine_path, 'wb') as f:
        f.write(engine.serialize())
    
    return engine
```

### 3. INT8 量化校准

```python
import tensorrt as trt
import numpy as np

class CalibrationDataset:
    def __init__(self, calibration_data):
        self.data = calibration_data
        self.current = 0
    
    def next_batch(self):
        if self.current < len(self.data):
            batch = self.data[self.current]
            self.current += 1
            return batch
        return None

class Int8Calibrator(trt.IInt8EntropyCalibrator2):
    def __init__(self, calibration_dataset, cache_file):
        trt.IInt8EntropyCalibrator2.__init__(self)
        self.calibration_dataset = calibration_dataset
        self.cache_file = cache_file
        self.device_input = cuda.mem_alloc(1 * 3 * 224 * 224 * 4)  # 假设输入尺寸
    
    def get_batch_size(self):
        return 1
    
    def get_batch(self, names):
        batch = self.calibration_dataset.next_batch()
        if batch is None:
            return None
        
        # 将数据复制到GPU
        cuda.memcpy_htod(self.device_input, batch.astype(np.float32))
        return [self.device_input]
    
    def read_calibration_cache(self):
        if os.path.exists(self.cache_file):
            with open(self.cache_file, "rb") as f:
                return f.read()
        return None
    
    def write_calibration_cache(self, cache):
        with open(self.cache_file, "wb") as f:
            f.write(cache)

def build_int8_engine(onnx_path, calibration_data, engine_path):
    """构建INT8量化引擎"""
    logger = trt.Logger(trt.Logger.WARNING)
    builder = trt.Builder(logger)
    network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
    parser = trt.OnnxParser(network, logger)
    
    # 解析ONNX
    with open(onnx_path, 'rb') as model:
        parser.parse(model.read())
    
    config = builder.create_builder_config()
    config.max_workspace_size = 1 << 30
    config.set_flag(trt.BuilderFlag.INT8)
    
    # 设置INT8校准器
    calibration_dataset = CalibrationDataset(calibration_data)
    config.int8_calibrator = Int8Calibrator(calibration_dataset, "calibration.cache")
    
    # 构建引擎
    engine = builder.build_engine(network, config)
    
    with open(engine_path, 'wb') as f:
        f.write(engine.serialize())
    
    return engine
```

---

## 推理引擎使用

### 异步推理

```python
import tensorrt as trt
import pycuda.driver as cuda
import threading
import queue

class AsyncTensorRTInference:
    def __init__(self, engine_path, max_batch_size=1):
        self.logger = trt.Logger(trt.Logger.WARNING)
        
        # 加载引擎
        with open(engine_path, 'rb') as f:
            self.engine = trt.Runtime(self.logger).deserialize_cuda_engine(f.read())
        
        # 创建多个执行上下文用于并发
        self.contexts = [self.engine.create_execution_context() for _ in range(2)]
        self.current_context = 0
        
        # 创建CUDA流
        self.streams = [cuda.Stream() for _ in range(2)]
        
        # 分配缓冲区
        self.allocate_buffers()
        
        # 结果队列
        self.result_queue = queue.Queue()
    
    def allocate_buffers(self):
        """为每个流分配独立的缓冲区"""
        self.buffer_sets = []
        
        for _ in range(len(self.streams)):
            inputs = []
            outputs = []
            bindings = []
            
            for binding in self.engine:
                size = trt.volume(self.engine.get_binding_shape(binding)) * self.engine.max_batch_size
                dtype = trt.nptype(self.engine.get_binding_dtype(binding))
                
                host_mem = cuda.pagelocked_empty(size, dtype)
                device_mem = cuda.mem_alloc(host_mem.nbytes)
                
                bindings.append(int(device_mem))
                
                if self.engine.binding_is_input(binding):
                    inputs.append({'host': host_mem, 'device': device_mem})
                else:
                    outputs.append({'host': host_mem, 'device': device_mem})
            
            self.buffer_sets.append({
                'inputs': inputs,
                'outputs': outputs,
                'bindings': bindings
            })
    
    def infer_async(self, input_data, callback=None):
        """异步推理"""
        def _infer():
            # 选择流和缓冲区
            stream_idx = self.current_context
            self.current_context = (self.current_context + 1) % len(self.streams)
            
            stream = self.streams[stream_idx]
            context = self.contexts[stream_idx]
            buffers = self.buffer_sets[stream_idx]
            
            # 异步复制输入数据
            np.copyto(buffers['inputs'][0]['host'], input_data.ravel())
            cuda.memcpy_htod_async(buffers['inputs'][0]['device'], 
                                  buffers['inputs'][0]['host'], stream)
            
            # 异步执行推理
            context.execute_async(batch_size=1, bindings=buffers['bindings'], stream_handle=stream.handle)
            
            # 异步复制输出数据
            cuda.memcpy_dtoh_async(buffers['outputs'][0]['host'], 
                                  buffers['outputs'][0]['device'], stream)
            
            # 同步流
            stream.synchronize()
            
            result = buffers['outputs'][0]['host'].copy()
            
            if callback:
                callback(result)
            else:
                self.result_queue.put(result)
        
        # 在单独线程中执行推理
        thread = threading.Thread(target=_infer)
        thread.start()
        return thread
    
    def get_result(self, timeout=None):
        """获取推理结果"""
        return self.result_queue.get(timeout=timeout)
```

### 批处理推理

```python
class BatchTensorRTInference:
    def __init__(self, engine_path, max_batch_size=8):
        self.logger = trt.Logger(trt.Logger.WARNING)
        self.max_batch_size = max_batch_size
        
        with open(engine_path, 'rb') as f:
            self.engine = trt.Runtime(self.logger).deserialize_cuda_engine(f.read())
        
        self.context = self.engine.create_execution_context()
        self.allocate_buffers()
    
    def infer_batch(self, input_batch):
        """批量推理"""
        batch_size = len(input_batch)
        if batch_size > self.max_batch_size:
            raise ValueError(f"Batch size {batch_size} exceeds maximum {self.max_batch_size}")
        
        # 准备批量输入
        batch_input = np.stack(input_batch, axis=0)
        
        # 复制到GPU
        np.copyto(self.inputs[0]['host'][:batch_input.size], batch_input.ravel())
        cuda.memcpy_htod(self.inputs[0]['device'], self.inputs[0]['host'])
        
        # 设置动态批大小
        self.context.set_binding_shape(0, batch_input.shape)
        
        # 执行推理
        self.context.execute_v2(bindings=self.bindings)
        
        # 获取结果
        cuda.memcpy_dtoh(self.outputs[0]['host'], self.outputs[0]['device'])
        
        # 解析批量输出
        output_shape = self.context.get_binding_shape(1)
        output_size = trt.volume(output_shape)
        
        results = []
        single_output_size = output_size // batch_size
        
        for i in range(batch_size):
            start_idx = i * single_output_size
            end_idx = start_idx + single_output_size
            result = self.outputs[0]['host'][start_idx:end_idx].copy()
            results.append(result.reshape(output_shape[1:]))
        
        return results
```

---

## 性能优化

### 1. 内存优化

```python
class MemoryOptimizedInference:
    def __init__(self, engine_path):
        self.logger = trt.Logger(trt.Logger.WARNING)
        
        with open(engine_path, 'rb') as f:
            self.engine = trt.Runtime(self.logger).deserialize_cuda_engine(f.read())
        
        self.context = self.engine.create_execution_context()
        
        # 预分配固定内存
        self.allocate_pinned_memory()
        
        # 创建内存池
        self.create_memory_pool()
    
    def allocate_pinned_memory(self):
        """分配页锁定内存提高传输效率"""
        self.inputs = []
        self.outputs = []
        self.bindings = []
        
        for binding in self.engine:
            size = trt.volume(self.engine.get_binding_shape(binding)) * self.engine.max_batch_size
            dtype = trt.nptype(self.engine.get_binding_dtype(binding))
            
            # 使用页锁定内存
            host_mem = cuda.pagelocked_empty(size, dtype)
            device_mem = cuda.mem_alloc(host_mem.nbytes)
            
            self.bindings.append(int(device_mem))
            
            if self.engine.binding_is_input(binding):
                self.inputs.append({'host': host_mem, 'device': device_mem})
            else:
                self.outputs.append({'host': host_mem, 'device': device_mem})
    
    def create_memory_pool(self):
        """创建内存池避免频繁分配"""
        self.memory_pool = []
        pool_size = 10  # 预分配10个缓冲区
        
        for _ in range(pool_size):
            input_buffer = np.empty_like(self.inputs[0]['host'])
            self.memory_pool.append(input_buffer)
        
        self.pool_index = 0
    
    def get_buffer_from_pool(self):
        """从内存池获取缓冲区"""
        buffer = self.memory_pool[self.pool_index]
        self.pool_index = (self.pool_index + 1) % len(self.memory_pool)
        return buffer
```

### 2. 预处理优化

```python
import cv2
import numpy as np

class OptimizedPreprocessor:
    def __init__(self, input_shape, mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]):
        self.input_shape = input_shape
        self.mean = np.array(mean, dtype=np.float32).reshape(1, 1, 3)
        self.std = np.array(std, dtype=np.float32).reshape(1, 1, 3)
        
        # 预分配输出缓冲区
        self.output_buffer = np.empty(input_shape, dtype=np.float32)
    
    def preprocess(self, image):
        """优化的图像预处理"""
        # 使用OpenCV的优化resize
        resized = cv2.resize(image, (self.input_shape[3], self.input_shape[2]), 
                           interpolation=cv2.INTER_LINEAR)
        
        # 归一化 (就地操作)
        normalized = resized.astype(np.float32) / 255.0
        normalized = (normalized - self.mean) / self.std
        
        # 转换维度 (H,W,C) -> (C,H,W)
        transposed = np.transpose(normalized, (2, 0, 1))
        
        # 复制到预分配的缓冲区
        np.copyto(self.output_buffer[0], transposed)
        
        return self.output_buffer

class GPUPreprocessor:
    """GPU加速预处理"""
    def __init__(self, input_shape):
        import cupy as cp  # 需要安装cupy
        
        self.cp = cp
        self.input_shape = input_shape
        
        # 在GPU上预分配内存
        self.gpu_buffer = cp.empty(input_shape, dtype=cp.float32)
        self.mean_gpu = cp.array([0.485, 0.456, 0.406], dtype=cp.float32).reshape(1, 3, 1, 1)
        self.std_gpu = cp.array([0.229, 0.224, 0.225], dtype=cp.float32).reshape(1, 3, 1, 1)
    
    def preprocess_gpu(self, image_gpu):
        """在GPU上执行预处理"""
        # 假设输入已经在GPU上
        resized = self.cp.ndimage.zoom(image_gpu, 
                                     (self.input_shape[2]/image_gpu.shape[0], 
                                      self.input_shape[3]/image_gpu.shape[1], 1))
        
        # 归一化
        normalized = resized.astype(self.cp.float32) / 255.0
        normalized = self.cp.transpose(normalized, (2, 0, 1))[None, ...]  # Add batch dim
        normalized = (normalized - self.mean_gpu) / self.std_gpu
        
        return normalized
```

### 3. 多GPU推理

```python
class MultiGPUInference:
    def __init__(self, engine_path, gpu_ids=[0, 1]):
        self.gpu_ids = gpu_ids
        self.engines = {}
        self.contexts = {}
        self.streams = {}
        
        # 为每个GPU创建引擎和上下文
        for gpu_id in gpu_ids:
            cuda.Device(gpu_id).make_context()
            
            # 加载引擎
            logger = trt.Logger(trt.Logger.WARNING)
            with open(engine_path, 'rb') as f:
                engine = trt.Runtime(logger).deserialize_cuda_engine(f.read())
            
            self.engines[gpu_id] = engine
            self.contexts[gpu_id] = engine.create_execution_context()
            self.streams[gpu_id] = cuda.Stream()
            
            cuda.Context.pop()
    
    def infer_multi_gpu(self, input_batches):
        """多GPU并行推理"""
        import concurrent.futures
        
        def infer_on_gpu(gpu_id, input_batch):
            cuda.Device(gpu_id).make_context()
            
            try:
                # 执行推理逻辑
                result = self._single_gpu_infer(gpu_id, input_batch)
                return result
            finally:
                cuda.Context.pop()
        
        # 并行执行
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(self.gpu_ids)) as executor:
            futures = []
            
            for i, gpu_id in enumerate(self.gpu_ids):
                if i < len(input_batches):
                    future = executor.submit(infer_on_gpu, gpu_id, input_batches[i])
                    futures.append(future)
            
            results = [future.result() for future in futures]
        
        return results
    
    def _single_gpu_infer(self, gpu_id, input_batch):
        """单GPU推理实现"""
        context = self.contexts[gpu_id]
        stream = self.streams[gpu_id]
        
        # 实现具体的推理逻辑
        # ...
        
        return result
```

---

## 完整代码示例

### 端到端图像分类示例

```python
#!/usr/bin/env python3
"""
TensorRT图像分类完整示例
支持ONNX模型转换、引擎构建、推理执行
"""

import numpy as np
import cv2
import tensorrt as trt
import pycuda.driver as cuda
import pycuda.autoinit
import time
import argparse
from pathlib import Path

class TensorRTClassifier:
    def __init__(self, engine_path=None, onnx_path=None, 
                 input_shape=(1, 3, 224, 224), num_classes=1000):
        """
        TensorRT图像分类器
        
        Args:
            engine_path: TensorRT引擎文件路径
            onnx_path: ONNX模型文件路径
            input_shape: 输入张量形状
            num_classes: 分类数量
        """
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.logger = trt.Logger(trt.Logger.WARNING)
        
        # 如果没有引擎文件，从ONNX构建
        if engine_path and Path(engine_path).exists():
            self.load_engine(engine_path)
        elif onnx_path and Path(onnx_path).exists():
            engine_path = onnx_path.replace('.onnx', '.engine')
            self.build_engine_from_onnx(onnx_path, engine_path)
            self.load_engine(engine_path)
        else:
            raise ValueError("需要提供有效的engine_path或onnx_path")
        
        # 分配内存缓冲区
        self.allocate_buffers()
        
        # 预处理参数
        self.mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        self.std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
    
    def build_engine_from_onnx(self, onnx_path, engine_path):
        """从ONNX构建TensorRT引擎"""
        print(f"从 {onnx_path} 构建TensorRT引擎...")
        
        builder = trt.Builder(self.logger)
        network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
        parser = trt.OnnxParser(network, self.logger)
        
        # 解析ONNX模型
        with open(onnx_path, 'rb') as model:
            if not parser.parse(model.read()):
                print('解析ONNX模型失败:')
                for error in range(parser.num_errors):
                    print(parser.get_error(error))
                return None
        
        # 配置构建参数
        config = builder.create_builder_config()
        config.max_workspace_size = 2 << 30  # 2GB工作空间
        
        # 启用FP16优化（如果支持）
        if builder.platform_has_fast_fp16:
            config.set_flag(trt.BuilderFlag.FP16)
            print("启用FP16精度优化")
        
        # 构建引擎
        print("正在构建引擎，这可能需要几分钟...")
        engine = builder.build_engine(network, config)
        
        if engine is None:
            print("引擎构建失败")
            return None
        
        # 保存引擎
        with open(engine_path, "wb") as f:
            f.write(engine.serialize())
        print(f"引擎已保存到 {engine_path}")
        
        return engine
    
    def load_engine(self, engine_path):
        """加载TensorRT引擎"""
        print(f"加载TensorRT引擎: {engine_path}")
        
        with open(engine_path, "rb") as f:
            self.engine = trt.Runtime(self.logger).deserialize_cuda_engine(f.read())
        
        self.context = self.engine.create_execution_context()
        print(f"引擎加载成功，输入形状: {self.engine.get_binding_shape(0)}")
    
    def allocate_buffers(self):
        """分配GPU和CPU内存缓冲区"""
        self.inputs = []
        self.outputs = []
        self.bindings = []
        
        for binding in self.engine:
            binding_idx = self.engine.get_binding_index(binding)
            size = trt.volume(self.engine.get_binding_shape(binding)) * self.engine.max_batch_size
            dtype = trt.nptype(self.engine.get_binding_dtype(binding))
            
            # 分配页锁定内存
            host_mem = cuda.pagelocked_empty(size, dtype)
            device_mem = cuda.mem_alloc(host_mem.nbytes)
            
            self.bindings.append(int(device_mem))
            
            if self.engine.binding_is_input(binding):
                self.inputs.append({
                    'host': host_mem, 
                    'device': device_mem, 
                    'name': binding,
                    'shape': self.engine.get_binding_shape(binding)
                })
            else:
                self.outputs.append({
                    'host': host_mem, 
                    'device': device_mem, 
                    'name': binding,
                    'shape': self.engine.get_binding_shape(binding)
                })
        
        print(f"内存分配完成 - 输入: {len(self.inputs)}, 输出: {len(self.outputs)}")
    
    def preprocess_image(self, image_path):
        """图像预处理"""
        # 读取图像
        if isinstance(image_path, str):
            image = cv2.imread(image_path)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image = image_path
        
        # 调整尺寸
        height, width = self.input_shape[2], self.input_shape[3]
        image = cv2.resize(image, (width, height), interpolation=cv2.INTER_LINEAR)
        
        # 归一化
        image = image.astype(np.float32) / 255.0
        image = (image - self.mean.reshape(1, 1, 3)) / self.std.reshape(1, 1, 3)
        
        # 转换维度并添加批次维度
        image = np.transpose(image, (2, 0, 1))  # HWC -> CHW
        image = np.expand_dims(image, axis=0)   # 添加批次维度
        
        return image
    
    def infer(self, input_data):
        """执行推理"""
        # 复制输入数据到GPU
        np.copyto(self.inputs[0]['host'], input_data.ravel())
        cuda.memcpy_htod(self.inputs[0]['device'], self.inputs[0]['host'])
        
        # 执行推理
        self.context.execute(batch_size=1, bindings=self.bindings)
        
        # 复制输出数据到CPU
        cuda.memcpy_dtoh(self.outputs[0]['host'], self.outputs[0]['device'])
        
        # 获取结果
        output_shape = self.outputs[0]['shape']
        result = self.outputs[0]['host'].reshape(output_shape)
        
        return result
    
    def predict(self, image_path, top_k=5):
        """完整的预测流程"""
        # 预处理
        start_time = time.time()
        input_data = self.preprocess_image(image_path)
        preprocess_time = time.time() - start_time
        
        # 推理
        start_time = time.time()
        output = self.infer(input_data)
        inference_time = time.time() - start_time
        
        # 后处理
        start_time = time.time()
        probabilities = self.softmax(output[0])
        top_indices = np.argsort(probabilities)[::-1][:top_k]
        top_probs = probabilities[top_indices]
        postprocess_time = time.time() - start_time
        
        # 性能统计
        total_time = preprocess_time + inference_time + postprocess_time
        
        results = {
            'predictions': [(idx, prob) for idx, prob in zip(top_indices, top_probs)],
            'timing': {
                'preprocess': preprocess_time * 1000,  # ms
                'inference': inference_time * 1000,    # ms
                'postprocess': postprocess_time * 1000, # ms
                'total': total_time * 1000             # ms
            }
        }
        
        return results
    
    def softmax(self, x):
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x))
        return exp_x / np.sum(exp_x)
    
    def benchmark(self, input_shape=None, num_runs=100):
        """性能基准测试"""
        if input_shape is None:
            input_shape = self.input_shape
        
        # 创建随机输入
        dummy_input = np.random.random(input_shape).astype(np.float32)
        
        # 预热
        print("预热中...")
        for _ in range(10):
            self.infer(dummy_input)
        
        # 基准测试
        print(f"开始基准测试 ({num_runs} 次运行)...")
        times = []
        
        for i in range(num_runs):
            start_time = time.time()
            self.infer(dummy_input)
            end_time = time.time()
            times.append((end_time - start_time) * 1000)  # 转换为毫秒
            
            if (i + 1) % 20 == 0:
                print(f"完成 {i + 1}/{num_runs}")
        
        # 统计结果
        times = np.array(times)
        results = {
            'mean': np.mean(times),
            'std': np.std(times),
            'min': np.min(times),
            'max': np.max(times),
            'p50': np.percentile(times, 50),
            'p95': np.percentile(times, 95),
            'p99': np.percentile(times, 99),
            'throughput': 1000 / np.mean(times)  # FPS
        }
        
        return results


def main():
    parser = argparse.ArgumentParser(description='TensorRT图像分类示例')
    parser.add_argument('--onnx', type=str, help='ONNX模型路径')
    parser.add_argument('--engine', type=str, help='TensorRT引擎路径')
    parser.add_argument('--image', type=str, help='测试图像路径')
    parser.add_argument('--benchmark', action='store_true', help='运行基准测试')
    parser.add_argument('--runs', type=int, default=100, help='基准测试运行次数')
    
    args = parser.parse_args()
    
    try:
        # 创建分类器
        classifier = TensorRTClassifier(
            engine_path=args.engine,
            onnx_path=args.onnx
        )
        
        if args.benchmark:
            # 性能基准测试
            print("="*50)
            print("性能基准测试")
            print("="*50)
            
            results = classifier.benchmark(num_runs=args.runs)
            
            print(f"\n基准测试结果 ({args.runs} 次运行):")
            print(f"平均延迟: {results['mean']:.2f} ± {results['std']:.2f} ms")
            print(f"最小延迟: {results['min']:.2f} ms")
            print(f"最大延迟: {results['max']:.2f} ms")
            print(f"P50 延迟: {results['p50']:.2f} ms")
            print(f"P95 延迟: {results['p95']:.2f} ms")
            print(f"P99 延迟: {results['p99']:.2f} ms")
            print(f"吞吐量: {results['throughput']:.1f} FPS")
        
        if args.image:
            # 图像预测
            print("="*50)
            print("图像分类预测")
            print("="*50)
            
            results = classifier.predict(args.image, top_k=5)
            
            print(f"\n图像: {args.image}")
            print("\nTop-5 预测结果:")
            for i, (idx, prob) in enumerate(results['predictions']):
                print(f"{i+1}. 类别 {idx}: {prob:.4f}")
            
            print(f"\n性能统计:")
            timing = results['timing']
            print(f"预处理: {timing['preprocess']:.2f} ms")
            print(f"推理: {timing['inference']:.2f} ms")
            print(f"后处理: {timing['postprocess']:.2f} ms")
            print(f"总计: {timing['total']:.2f} ms")
    
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
```

### 使用示例

```bash
# 从ONNX模型构建引擎并进行预测
python tensorrt_classifier.py --onnx resnet50.onnx --image test.jpg

# 使用已有引擎进行预测
python tensorrt_classifier.py --engine resnet50.engine --image test.jpg

# 运行性能基准测试
python tensorrt_classifier.py --engine resnet50.engine --benchmark --runs 200
```

---

## 故障排除

### 常见问题和解决方案

#### 1. 安装问题

```bash
# 问题: ImportError: No module named 'tensorrt'
# 解决方案: 检查安装路径和Python版本
python -c "import sys; print(sys.path)"
pip list | grep tensorrt

# 重新安装
pip uninstall tensorrt
pip install nvidia-tensorrt
```

#### 2. CUDA 兼容性问题

```python
import tensorrt as trt
import pycuda.driver as cuda

def check_cuda_compatibility():
    """检查CUDA兼容性"""
    try:
        # 检查CUDA版本
        print(f"PyCUDA version: {cuda.get_version()}")
        print(f"CUDA driver version: {cuda.get_driver_version()}")
        
        # 检查GPU设备
        cuda.init()
        num_gpus = cuda.Device.count()
        print(f"Available GPUs: {num_gpus}")
        
        for i in range(num_gpus):
            device = cuda.Device(i)
            print(f"GPU {i}: {device.name()}")
            print(f"  Compute capability: {device.compute_capability()}")
            print(f"  Total memory: {device.total_memory() // 1024**2} MB")
        
        # 检查TensorRT版本
        print(f"TensorRT version: {trt.__version__}")
        
        return True
    except Exception as e:
        print(f"CUDA兼容性检查失败: {e}")
        return False

check_cuda_compatibility()
```

#### 3. 内存问题

```python
class MemoryProfiler:
    """内存使用分析器"""
    
    def __init__(self):
        self.cuda_context = cuda.Device(0).make_context()
    
    def get_memory_info(self):
        """获取GPU内存使用信息"""
        free_mem, total_mem = cuda.mem_get_info()
        used_mem = total_mem - free_mem
        
        return {
            'total': total_mem // 1024**2,    # MB
            'used': used_mem // 1024**2,      # MB
            'free': free_mem // 1024**2,      # MB
            'usage_percent': (used_mem / total_mem) * 100
        }
    
    def monitor_memory(self, func, *args, **kwargs):
        """监控函数执行期间的内存使用"""
        mem_before = self.get_memory_info()
        print(f"执行前内存使用: {mem_before['used']} MB ({mem_before['usage_percent']:.1f}%)")
        
        result = func(*args, **kwargs)
        
        mem_after = self.get_memory_info()
        print(f"执行后内存使用: {mem_after['used']} MB ({mem_after['usage_percent']:.1f}%)")
        print(f"内存增长: {mem_after['used'] - mem_before['used']} MB")
        
        return result
    
    def cleanup(self):
        """清理CUDA上下文"""
        self.cuda_context.pop()

# 使用示例
profiler = MemoryProfiler()
result = profiler.monitor_memory(classifier.infer, input_data)
profiler.cleanup()
```

#### 4. 引擎构建失败

```python
def debug_engine_build(onnx_path):
    """调试引擎构建过程"""
    logger = trt.Logger(trt.Logger.VERBOSE)  # 详细日志
    builder = trt.Builder(logger)
    network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
    parser = trt.OnnxParser(network, logger)
    
    # 解析ONNX
    with open(onnx_path, 'rb') as model:
        if not parser.parse(model.read()):
            print("ONNX解析失败:")
            for error in range(parser.num_errors):
                print(f"  错误 {error}: {parser.get_error(error)}")
            return None
    
    # 检查网络
    print(f"网络层数: {network.num_layers}")
    print(f"输入数量: {network.num_inputs}")
    print(f"输出数量: {network.num_outputs}")
    
    for i in range(network.num_inputs):
        input_tensor = network.get_input(i)
        print(f"输入 {i}: {input_tensor.name}, 形状: {input_tensor.shape}, 类型: {input_tensor.dtype}")
    
    for i in range(network.num_outputs):
        output_tensor = network.get_output(i)
        print(f"输出 {i}: {output_tensor.name}, 形状: {output_tensor.shape}, 类型: {output_tensor.dtype}")
    
    # 配置构建参数
    config = builder.create_builder_config()
    config.max_workspace_size = 1 << 30
    
    # 尝试构建
    print("开始构建引擎...")
    engine = builder.build_engine(network, config)
    
    if engine:
        print("引擎构建成功!")
        return engine
    else:
        print("引擎构建失败!")
        return None
```

#### 5. 精度问题

```python
def compare_outputs(onnx_path, engine_path, test_input):
    """比较ONNX和TensorRT输出差异"""
    import onnxruntime as ort
    
    # ONNX推理
    ort_session = ort.InferenceSession(onnx_path)
    onnx_outputs = ort_session.run(None, {'input': test_input})
    
    # TensorRT推理
    classifier = TensorRTClassifier(engine_path=engine_path)
    trt_outputs = classifier.infer(test_input)
    
    # 比较输出
    for i, (onnx_out, trt_out) in enumerate(zip(onnx_outputs, [trt_outputs])):
        diff = np.abs(onnx_out - trt_out)
        max_diff = np.max(diff)
        mean_diff = np.mean(diff)
        
        print(f"输出 {i}:")
        print(f"  最大差异: {max_diff}")
        print(f"  平均差异: {mean_diff}")
        print(f"  ONNX范围: [{np.min(onnx_out):.6f}, {np.max(onnx_out):.6f}]")
        print(f"  TRT范围: [{np.min(trt_out):.6f}, {np.max(trt_out):.6f}]")
        
        if max_diff > 1e-3:
            print(f"  警告: 输出差异较大!")
```

---

## 最佳实践

### 1. 性能优化建议

```python
class PerformanceOptimizationGuide:
    """TensorRT性能优化最佳实践"""
    
    @staticmethod
    def optimize_engine_build():
        """引擎构建优化"""
        tips = [
            "1. 使用适当的工作空间大小 (1-4GB)",
            "2. 启用FP16精度 (如果GPU支持)",
            "3. 考虑INT8量化 (对于延迟敏感应用)",
            "4. 设置合适的批处理大小",
            "5. 使用动态形状 (如果输入尺寸变化)",
            "6. 启用DLA (对于Jetson设备)",
            "7. 使用层融合标志",
            "8. 优化输入格式 (NCHW vs NHWC)"
        ]
        return tips
    
    @staticmethod
    def optimize_inference():
        """推理执行优化"""
        tips = [
            "1. 使用页锁定内存",
            "2. 预分配缓冲区避免动态分配",
            "3. 使用CUDA流实现异步执行",
            "4. 批处理多个输入",
            "5. 复用执行上下文",
            "6. 预处理优化 (OpenCV, GPU加速)",
            "7. 避免频繁的CPU-GPU数据传输",
            "8. 使用多个推理实例实现并行"
        ]
        return tips
    
    @staticmethod
    def memory_optimization():
        """内存优化建议"""
        tips = [
            "1. 监控GPU内存使用情况",
            "2. 使用内存池避免频繁分配",
            "3. 及时释放不使用的缓冲区",
            "4. 考虑模型剪枝减少内存占用",
            "5. 使用量化减少模型大小",
            "6. 共享缓冲区 (如果可能)",
            "7. 避免内存泄漏",
            "8. 使用CUDA虚拟内存 (如果支持)"
        ]
        return tips

# 性能检查清单
def performance_checklist():
    """性能优化检查清单"""
    print("TensorRT性能优化检查清单:\n")
    
    guide = PerformanceOptimizationGuide()
    
    print("引擎构建优化:")
    for tip in guide.optimize_engine_build():
        print(f"  □ {tip}")
    
    print("\n推理执行优化:")
    for tip in guide.optimize_inference():
        print(f"  □ {tip}")
    
    print("\n内存优化:")
    for tip in guide.memory_optimization():
        print(f"  □ {tip}")
```

### 2. 生产部署建议

```python
class ProductionDeploymentGuide:
    """生产环境部署指南"""
    
    @staticmethod
    def deployment_checklist():
        """部署检查清单"""
        return [
            "✓ GPU驱动和CUDA版本兼容性",
            "✓ TensorRT版本与模型兼容性",
            "✓ 足够的GPU内存",
            "✓ 模型精度验证",
            "✓ 性能基准测试",
            "✓ 错误处理机制",
            "✓ 监控和日志记录",
            "✓ 容器化部署配置",
            "✓ 负载均衡策略",
            "✓ 自动扩缩容配置"
        ]
    
    @staticmethod
    def docker_example():
        """Docker部署示例"""
        dockerfile = """
# Dockerfile for TensorRT application
FROM nvcr.io/nvidia/tensorrt:22.12-py3

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制应用代码
COPY . /app
WORKDIR /app

# 暴露端口
EXPOSE 8000

# 运行应用
CMD ["python", "server.py"]
"""
        
        docker_compose = """
# docker-compose.yml
version: '3.8'
services:
  tensorrt-app:
    build: .
    ports:
      - "8000:8000"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    environment:
      - CUDA_VISIBLE_DEVICES=0
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
"""
        
        return dockerfile, docker_compose
    
    @staticmethod
    def monitoring_setup():
        """监控设置示例"""
        monitoring_code = """
import psutil
import GPUtil
import time
import logging

class SystemMonitor:
    def __init__(self):
        self.logger = logging.getLogger('system_monitor')
    
    def log_system_stats(self):
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        
        # GPU使用率
        gpus = GPUtil.getGPUs()
        
        for gpu in gpus:
            self.logger.info(f"GPU {gpu.id}: {gpu.load*100:.1f}% load, "
                           f"{gpu.memoryUsed}/{gpu.memoryTotal} MB memory")
        
        self.logger.info(f"CPU: {cpu_percent}%, "
                        f"Memory: {memory.percent}%")
"""
        
        return monitoring_code

# 使用示例
guide = ProductionDeploymentGuide()
print("生产部署检查清单:")
for item in guide.deployment_checklist():
    print(f"  {item}")
```

### 3. 错误处理和日志记录

```python
import logging
import traceback
from contextlib import contextmanager

class TensorRTLogger:
    """增强的TensorRT日志系统"""
    
    def __init__(self, log_level=logging.INFO):
        self.setup_logging(log_level)
        self.trt_logger = self.create_trt_logger()
    
    def setup_logging(self, log_level):
        """设置Python日志系统"""
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('tensorrt_inference.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('TensorRT')
    
    def create_trt_logger(self):
        """创建TensorRT日志记录器"""
        class CustomTRTLogger(trt.ILogger):
            def __init__(self, python_logger):
                trt.ILogger.__init__(self)
                self.python_logger = python_logger
            
            def log(self, severity, msg):
                if severity == trt.Logger.INTERNAL_ERROR:
                    self.python_logger.error(f"[TRT INTERNAL_ERROR]: {msg}")
                elif severity == trt.Logger.ERROR:
                    self.python_logger.error(f"[TRT ERROR]: {msg}")
                elif severity == trt.Logger.WARNING:
                    self.python_logger.warning(f"[TRT WARNING]: {msg}")
                elif severity == trt.Logger.INFO:
                    self.python_logger.info(f"[TRT INFO]: {msg}")
                elif severity == trt.Logger.VERBOSE:
                    self.python_logger.debug(f"[TRT VERBOSE]: {msg}")
        
        return CustomTRTLogger(self.logger)
    
    @contextmanager
    def error_handling(self, operation_name):
        """统一错误处理"""
        try:
            self.logger.info(f"开始执行: {operation_name}")
            start_time = time.time()
            yield
            elapsed_time = time.time() - start_time
            self.logger.info(f"完成执行: {operation_name} ({elapsed_time:.2f}s)")
        except Exception as e:
            self.logger.error(f"执行失败: {operation_name}")
            self.logger.error(f"错误详情: {str(e)}")
            self.logger.error(f"堆栈跟踪:\n{traceback.format_exc()}")
            raise

# 使用示例
logger_system = TensorRTLogger()

with logger_system.error_handling("模型推理"):
    result = classifier.infer(input_data)
```

---

## 总结

本文档提供了Python TensorRT SDK的全面指南，涵盖了从基础安装到高级优化的所有方面。通过遵循这些最佳实践和示例代码，您可以有效地利用TensorRT的强大功能来加速深度学习模型推理。

### 关键要点

1. **合适的精度选择**: 根据应用需求选择FP32、FP16或INT8
2. **内存优化**: 使用页锁定内存和预分配缓冲区
3. **异步执行**: 利用CUDA流实现并行处理
4. **批处理**: 通过批量推理提高吞吐量
5. **监控和调试**: 建立完善的日志和监控系统

记住，TensorRT的性能优化是一个迭代过程，需要根据具体的模型和硬件环境进行调整和测试。