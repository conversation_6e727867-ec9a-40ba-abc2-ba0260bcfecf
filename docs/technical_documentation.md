# MMPose Flask 技术文档

## 代码结构

```
mmpose_flask/
├── service.py            # Flask 服务主文件
├── trt_model.py          # 姿态检测模型封装
├── Tensorrt.py           # TensorRT 引擎封装
├── action_filters.py     # 行为过滤器实现
├── cyddh_filter.py       # 备用行为过滤器实现
├── dataflow.md           # 数据流程图
├── SmokingFilter.md      # 吸烟检测流程图
└── 1.engine              # TensorRT 模型文件
```

## 核心模块详解

### 1. TensorRT 引擎封装 (Tensorrt.py)

#### TrtModelMMPose 类

该类将 TensorRT 引擎封装为 PyTorch 模块，实现了 `torch.nn.Module` 接口。

```python
class TrtModelMMPose(torch.nn.Module):
    def __init__(self, engine: Union[str, trt.ICudaEngine], output_names: Optional[Sequence[str]] = None) -> None:
        # 初始化 TensorRT 引擎
        # 如果 engine 是字符串，则加载引擎文件
        # 否则直接使用传入的引擎对象
        
    def forward(self, inputs: Dict[str, torch.Tensor]):
        # 执行模型推理
        # 1. 检查输入张量形状是否符合要求
        # 2. 设置输入绑定
        # 3. 创建输出张量
        # 4. 执行异步推理
        # 5. 返回预测结果
```

关键实现细节：
- 支持从文件加载 TensorRT 引擎或直接使用引擎对象
- 自动检测输入和输出名称
- 检查输入张量形状是否符合引擎配置
- 使用 CUDA 流进行异步推理，提高性能

#### select_device 函数

该函数用于选择合适的计算设备（CPU 或 GPU）。

```python
def select_device(device='', apex=False, batch_size=None):
    # 根据参数选择 CPU 或 GPU
    # 检查 CUDA 可用性
    # 打印设备信息
    # 返回 torch.device 对象
```

### 2. 姿态检测器 (trt_model.py)

#### preprocessing 函数

该函数将输入图像转换为模型所需的格式。

```python
def preprocessing(ori_img):
    # 使用 torchvision.transforms 进行图像预处理
    # 1. 转换为 PIL 图像
    # 2. 调整大小为 256x192
    # 3. 转换为张量
    # 4. 标准化
    # 5. 添加批次维度并移动到 GPU
```

标准化参数：
- 均值：[0.485, 0.456, 0.406]
- 标准差：[0.229, 0.224, 0.225]

#### PoseDetector 类

该类实现了姿态检测的核心功能。

```python
class PoseDetector(object):
    def __init__(self, weights, device):
        # 初始化姿态检测器
        # 加载 TensorRT 模型
        
    def _warm_up(self):
        # 模型预热，减少首次推理延迟
        
    def predict(self, img):
        # 执行模型推理
        # 1. 预处理图像
        # 2. 调用模型
        # 3. 返回预测结果
        
    def postprocessing(self, outputs, ori_img_w, ori_img_h):
        # 后处理模型输出
        # 1. 从热力图中提取关键点坐标和置信度
        # 2. 将坐标映射回原始图像尺寸
        # 3. 返回关键点坐标和置信度
```

后处理关键实现：
- 对每个热力图（1×17×48×64）找到最大值位置
- 记录最大值作为关键点置信度
- 将热力图坐标映射回原始图像坐标系

### 3. 行为过滤器 (action_filters.py)

#### CommonTools 类

该类提供了行为过滤器的通用工具方法。

```python
class CommonTools:
    def __init__(self):
        pass
        
    def calculate_angle(self, vecA, vecB):
        # 计算两个向量之间的夹角
        
    def calculate_distance(self, pointA, pointB):
        # 计算两点之间的欧氏距离
        
    def judge_position(self):
        # 位置判断（未实现）
```

#### SmokingFilter 类

该类实现了吸烟行为检测。

```python
class SmokingFilter(CommonTools):
    def __init__(self, keypoints, scores:list, center_point):
        # 初始化过滤器
        # keypoints: 关键点坐标
        # scores: 关键点置信度
        # center_point: 香烟中心点
        
    def set_score_thresh(self, thresh):
        # 设置置信度阈值
        
    def filter(self, angle_thresh=60):
        # 执行吸烟行为检测
        # 1. 检查关键点置信度
        # 2. 检查香烟位置
        # 3. 计算手臂角度
        # 4. 判断是否为吸烟姿势
```

检测算法详解：
1. 检查至少 9 个关键点的置信度是否大于阈值（默认 0.2）
2. 计算香烟中心点到鼻子的距离，与双眼距离比较
3. 如果香烟距离鼻子太远（>3倍眼距）或在鼻子上方，则不是吸烟
4. 计算左右手臂角度（肘部-手腕与肘部-肩膀的夹角）
5. 如果至少一只手臂角度小于阈值（默认 60°），选择角度较小的手臂
6. 检查手腕位置是否符合吸烟姿势

#### PhoningFilter 类

该类实现了打电话行为检测。

```python
class PhoningFilter(CommonTools):
    def __init__(self, keypoints, scores:list, center_point):
        # 初始化过滤器
        # keypoints: 关键点坐标
        # scores: 关键点置信度
        # center_point: 电话中心点
        
    def set_score_thresh(self, thresh):
        # 设置置信度阈值
        
    def filter(self, angle_thresh=60):
        # 执行打电话行为检测
        # 1. 检查关键点置信度
        # 2. 检查电话位置
        # 3. 计算手臂角度
        # 4. 判断是否为打电话姿势
```

检测算法详解：
1. 检查至少 9 个关键点的置信度是否大于阈值（默认 0.2）
2. 计算电话中心点到耳朵中心的距离，与肩宽比较
3. 如果电话距离耳朵太远（>0.8倍肩宽），则不是打电话
4. 计算左右手臂角度（肘部-手腕与肘部-肩膀的夹角）
5. 如果至少一只手臂角度小于阈值（默认 60°），选择角度较小的手臂
6. 检查手腕位置是否符合打电话姿势（手腕到耳朵的距离适中，且手腕到鼻子的距离较大）

#### FallingFilter 类

该类实现了摔倒行为检测。

```python
class FallingFilter(CommonTools):
    def __init__(self, keypoints, scores:list):
        # 初始化过滤器
        # keypoints: 关键点坐标
        # scores: 关键点置信度
        
    def set_score_thresh(self, thresh):
        # 设置置信度阈值
        
    def filter(self, angle_thresh=60):
        # 执行摔倒行为检测
        # 1. 检查关键点置信度
        # 2. 计算身体各部分与水平线的夹角
        # 3. 计算上下身体比例
        # 4. 判断是否为摔倒姿势
```

检测算法详解：
1. 检查至少 9 个关键点的置信度是否大于阈值（默认 0.2）
2. 计算身体各部分的中心点（耳朵中心、臀部中心、脚踝中心）
3. 计算上下身体比例（耳朵中心到臀部中心的距离 / 臀部中心到脚踝中心的距离）
4. 计算上半身和下半身与水平线的夹角
5. 如果上下半身与水平线的夹角均小于 45°，且比例在特定范围内（0.9 < p < 1.6），则判断为摔倒

### 4. Web 服务 (service.py)

该文件实现了 Flask Web 服务，提供 REST API 接口。

```python
# 初始化 Flask 应用和姿态检测器
app = Flask(__name__)
pose_detector = PoseDetector(WEIGHTS, DEVICE)

# 打电话检测接口
@app.route('/phoning', methods=['POST'])
def phoning_detect():
    # 解析请求数据
    # 执行姿态检测
    # 执行打电话行为检测
    # 返回检测结果

# 吸烟检测接口
@app.route('/smoking', methods=['POST'])
def smoking_detect():
    # 解析请求数据
    # 执行姿态检测
    # 执行吸烟行为检测
    # 返回检测结果

# 摔倒检测接口
@app.route('/falling', methods=['POST'])
def falling_detect():
    # 解析请求数据
    # 执行姿态检测
    # 执行摔倒行为检测
    # 返回检测结果

# 启动服务
app.run(host='0.0.0.0', port=20249)
```

请求处理流程：
1. 解析 JSON 请求数据，提取 Base64 编码的图像和其他参数
2. 解码图像并转换为 RGB 格式
3. 使用姿态检测器提取关键点
4. 使用相应的行为过滤器判断行为
5. 返回 JSON 格式的检测结果

## 性能优化技术

### 1. TensorRT 加速

项目使用 TensorRT 优化深度学习模型，相比原始 PyTorch 模型，推理速度提升显著。

关键优化点：
- 模型量化：降低计算精度，提高速度
- 计算图优化：融合操作，减少内存访问
- 内核自动调优：针对特定硬件优化计算

### 2. 异步推理

使用 CUDA 流进行异步推理，提高 GPU 利用率。

```python
self.context.execute_async_v2(bindings, torch.cuda.current_stream().cuda_stream)
```

### 3. 模型预热

通过预热减少首次推理的延迟。

```python
def _warm_up(self):
    self.model(dict(input=torch.randn(1, 3, self.input_shape[0], self.input_shape[1]).to(DEVICE)))
```

### 4. 内存优化

重用张量，减少内存分配和释放开销。

```python
# 创建输出张量时固定形状，避免动态分配
shape = tuple((1, 17, 64, 48))
output = torch.empty(size=shape, dtype=dtype, device=device)
```

## 关键点索引映射

```
{
    "nose": 0,
    "left_eye": 1,
    "right_eye": 2,
    "left_ear": 3,
    "right_ear": 4,
    "left_shoulder": 5,
    "right_shoulder": 6,
    "left_elbow": 7,
    "right_elbow": 8,
    "left_wrist": 9,
    "right_wrist": 10,
    "left_hip": 11,
    "right_hip": 12,
    "left_knee": 13,
    "right_knee": 14,
    "left_ankle": 15,
    "right_ankle": 16
}
```

## API 请求/响应示例

### 打电话检测

请求：
```json
{
    "image": "base64编码的图像数据...",
    "boxLocX": 100,
    "boxLocY": 150
}
```

响应：
```json
{
    "result": true
}
```

### 吸烟检测

请求：
```json
{
    "image": "base64编码的图像数据...",
    "boxLocX": 120,
    "boxLocY": 160
}
```

响应：
```json
{
    "result": false
}
```

### 摔倒检测

请求：
```json
{
    "image": "base64编码的图像数据..."
}
```

响应：
```json
{
    "result": true
}
```

## 错误处理

当前实现中的错误处理较为简单，主要依赖于 Flask 的默认错误处理机制。建议在生产环境中增强错误处理，包括：

1. 请求参数验证
2. 异常捕获和日志记录
3. 友好的错误响应

## 扩展建议

### 1. 添加新的行为过滤器

继承 `CommonTools` 类并实现 `filter` 方法：

```python
class NewActionFilter(CommonTools):
    def __init__(self, keypoints, scores:list, ...):
        super().__init__()
        self.threshold = 0.2
        self.keypoints = np.array(keypoints)
        self.scores = scores
        # 其他初始化
        
    def filter(self, ...):
        # 实现检测逻辑
        # 返回布尔值表示是否检测到行为
```

### 2. 添加新的 API 接口

在 `service.py` 中添加新的路由和处理函数：

```python
@app.route('/new_action', methods=['POST'])
def new_action_detect():
    # 解析请求数据
    data = json.loads(request.data)
    img_base64 = data['image']
    # 其他参数...
    
    # 解码图像
    img_binary = base64.b64decode(img_base64)
    nparr = np.frombuffer(img_binary, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    # 执行姿态检测
    h, w, _ = img.shape
    outs = pose_detector.predict(img)
    k, s = pose_detector.postprocessing(outs, w, h)
    
    # 执行行为检测
    pfilter = NewActionFilter(k, s, ...)
    result = pfilter.filter()
    
    # 返回结果
    return jsonify({'result': result})
```

### 3. 模型更新

如果需要更新模型，需要：

1. 替换 `1.engine` 文件
2. 根据新模型的输入/输出形状调整 `TrtModelMMPose` 类中的相关参数
3. 如果关键点定义发生变化，更新行为过滤器中的关键点索引
