# MMPose Flask 部署指南

## 系统要求

### 硬件要求

- **CPU**: 至少 4 核心
- **内存**: 至少 8GB RAM
- **GPU**: NVIDIA GPU，支持 CUDA 和 TensorRT（推荐 GTX 1060 或更高）
- **存储**: 至少 5GB 可用空间

### 软件要求

- **操作系统**: Ubuntu 18.04/20.04 LTS 或 Windows 10 (带 WSL2)
- **CUDA**: 10.2 或更高版本
- **cuDNN**: 与 CUDA 版本兼容的版本
- **TensorRT**: 7.0 或更高版本
- **Python**: 3.8 或更高版本

## 安装步骤

### 1. 安装 CUDA 和 cuDNN

请参考 NVIDIA 官方文档安装 CUDA 和 cuDNN：
- [CUDA 安装指南](https://docs.nvidia.com/cuda/cuda-installation-guide-linux/index.html)
- [cuDNN 安装指南](https://docs.nvidia.com/deeplearning/cudnn/install-guide/index.html)

### 2. 安装 TensorRT

请参考 NVIDIA 官方文档安装 TensorRT：
- [TensorRT 安装指南](https://docs.nvidia.com/deeplearning/tensorrt/install-guide/index.html)

### 3. 创建 Python 虚拟环境

```bash
# 安装 virtualenv
pip install virtualenv

# 创建虚拟环境
virtualenv -p python3.8 venv

# 激活虚拟环境
# Linux/macOS
source venv/bin/activate
# Windows
venv\Scripts\activate
```

### 4. 安装依赖包

```bash
# 安装 PyTorch (根据您的 CUDA 版本选择合适的命令)
# 对于 CUDA 11.3
pip install torch==1.10.0+cu113 torchvision==0.11.1+cu113 -f https://download.pytorch.org/whl/cu113/torch_stable.html

# 安装其他依赖
pip install flask opencv-python numpy
```

### 5. 下载项目代码

```bash
git clone https://github.com/your-username/mmpose_flask.git
cd mmpose_flask
```

或者直接下载并解压项目代码。

### 6. 准备模型文件

将 TensorRT 模型文件 `1.engine` 放置在项目根目录下。如果您没有预先转换好的 TensorRT 模型，请参考 [模型转换](#模型转换) 部分。

## 配置

### 服务配置

编辑 `service.py` 文件，根据需要修改以下配置：

```python
# 设备选择
DEVICE = select_device('0')  # 使用第一个 GPU，可以修改为 'cpu' 或其他 GPU 索引

# 模型文件路径
WEIGHTS = '1.engine'  # 可以修改为您的模型文件路径

# 服务端口
app.run(host='0.0.0.0', port=20249)  # 可以修改监听地址和端口
```

### 行为过滤器配置

如果需要调整行为检测的灵敏度，可以修改 `action_filters.py` 中的阈值参数：

```python
# 修改置信度阈值
self.smoking_threshold = 0.2  # 可以根据需要调整
self.phoning_threshold = 0.2  # 可以根据需要调整
self.falling_threshold = 0.2  # 可以根据需要调整

# 修改角度阈值
def filter(self, angle_thresh=60):  # 可以根据需要调整
```

## 启动服务

```bash
# 确保在虚拟环境中
python service.py
```

如果一切正常，您应该能看到类似以下的输出：

```
Using CUDA device0 _CudaDeviceProperties(name='NVIDIA GeForce GTX 1080', total_memory=8192MB)

 * Serving Flask app "service" (lazy loading)
 * Environment: production
   WARNING: This is a development server. Do not use it in a production deployment.
   Use a production WSGI server instead.
 * Debug mode: off
 * Running on http://0.0.0.0:20249/ (Press CTRL+C to quit)
```

## 生产环境部署

对于生产环境，建议使用 WSGI 服务器（如 Gunicorn）和反向代理（如 Nginx）来部署 Flask 应用。

### 使用 Gunicorn 部署

1. 安装 Gunicorn

```bash
pip install gunicorn
```

2. 创建 WSGI 入口文件 `wsgi.py`

```python
from service import app

if __name__ == "__main__":
    app.run()
```

3. 启动 Gunicorn

```bash
gunicorn --bind 0.0.0.0:20249 wsgi:app --workers 4
```

### 使用 Nginx 作为反向代理

1. 安装 Nginx

```bash
# Ubuntu
sudo apt-get install nginx

# CentOS
sudo yum install nginx
```

2. 配置 Nginx

创建配置文件 `/etc/nginx/sites-available/mmpose_flask`：

```nginx
server {
    listen 80;
    server_name your_domain.com;  # 替换为您的域名或 IP

    location / {
        proxy_pass http://127.0.0.1:20249;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

3. 启用配置并重启 Nginx

```bash
# 创建符号链接
sudo ln -s /etc/nginx/sites-available/mmpose_flask /etc/nginx/sites-enabled/

# 检查配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

## 使用 Docker 部署

### 创建 Dockerfile

在项目根目录创建 `Dockerfile`：

```dockerfile
FROM nvcr.io/nvidia/tensorrt:21.08-py3

WORKDIR /app

# 安装依赖
RUN pip install flask opencv-python numpy torch torchvision

# 复制项目文件
COPY . /app/

# 暴露端口
EXPOSE 20249

# 启动服务
CMD ["python", "service.py"]
```

### 构建和运行 Docker 镜像

```bash
# 构建镜像
docker build -t mmpose_flask .

# 运行容器
docker run --gpus all -p 20249:20249 mmpose_flask
```

## 模型转换

如果您没有预先转换好的 TensorRT 模型，可以按照以下步骤将 PyTorch 模型转换为 TensorRT 模型：

1. 安装 ONNX

```bash
pip install onnx onnx-simplifier
```

2. 将 PyTorch 模型转换为 ONNX 格式

```python
import torch
import torchvision

# 加载 PyTorch 模型
model = torchvision.models.resnet50(pretrained=True)
model.eval()

# 创建示例输入
dummy_input = torch.randn(1, 3, 256, 192, device='cuda')

# 导出 ONNX 模型
torch.onnx.export(model, dummy_input, "model.onnx",
                  input_names=["input"],
                  output_names=["output"],
                  dynamic_axes={'input': {0: 'batch_size'},
                               'output': {0: 'batch_size'}})
```

3. 使用 TensorRT 将 ONNX 模型转换为 TensorRT 引擎

```python
import tensorrt as trt
import os

def build_engine(onnx_file_path, engine_file_path):
    """
    将 ONNX 模型转换为 TensorRT 引擎
    """
    TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
    builder = trt.Builder(TRT_LOGGER)
    network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
    parser = trt.OnnxParser(network, TRT_LOGGER)
    
    with open(onnx_file_path, 'rb') as model:
        if not parser.parse(model.read()):
            print('ERROR: Failed to parse the ONNX file.')
            for error in range(parser.num_errors):
                print(parser.get_error(error))
            return None
    
    config = builder.create_builder_config()
    config.max_workspace_size = 1 << 30  # 1GB
    
    # 设置 FP16 精度 (可选)
    if builder.platform_has_fast_fp16:
        config.set_flag(trt.BuilderFlag.FP16)
    
    engine = builder.build_engine(network, config)
    
    with open(engine_file_path, 'wb') as f:
        f.write(engine.serialize())
    
    return engine

# 转换模型
build_engine("model.onnx", "1.engine")
```

## 故障排除

### 1. CUDA 相关错误

**问题**: `CUDA error: no kernel image is available for execution on the device`

**解决方案**:
- 确保 CUDA 和 cuDNN 正确安装
- 检查 GPU 驱动版本是否与 CUDA 版本兼容
- 尝试使用兼容的 PyTorch 版本

### 2. TensorRT 相关错误

**问题**: `Failed to load engine from file`

**解决方案**:
- 确保 TensorRT 模型文件 `1.engine` 存在且路径正确
- 检查 TensorRT 版本是否兼容
- 尝试重新转换模型

### 3. 内存不足

**问题**: `CUDA out of memory`

**解决方案**:
- 减小批处理大小
- 使用更小的模型
- 升级 GPU 内存

### 4. 服务无法启动

**问题**: `Address already in use`

**解决方案**:
- 检查端口 20249 是否已被占用
- 修改 `service.py` 中的端口号
- 终止占用端口的进程

## 性能优化

### 1. 批处理处理

如果需要同时处理多个请求，可以考虑实现批处理机制，将多个图像一次性送入模型进行推理。

### 2. 模型量化

使用 TensorRT 的 INT8 量化可以进一步提高推理速度，但可能会略微降低精度。

### 3. 多线程处理

使用多线程处理请求，可以提高服务的并发处理能力。

## 监控与日志

### 添加日志记录

修改 `service.py`，添加日志记录：

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("mmpose_flask.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 在关键位置添加日志
@app.route('/phoning', methods=['POST'])
def phoning_detect():
    start = time.time()
    logger.info("Received phoning detection request")
    
    # 处理逻辑...
    
    end = time.time()
    logger.info(f"Phoning detection completed in {end - start:.2f}s, result: {is_phoning}")
    return jsonify({'result': is_phoning})
```

### 使用监控工具

可以使用 Prometheus 和 Grafana 等工具监控服务的性能和健康状况。

## 安全考虑

### 1. 输入验证

添加输入验证，确保请求参数符合预期：

```python
@app.route('/phoning', methods=['POST'])
def phoning_detect():
    try:
        data = json.loads(request.data)
        
        # 验证必要参数
        if 'image' not in data or 'boxLocX' not in data or 'boxLocY' not in data:
            return jsonify({'error': 'Missing required parameters'}), 400
        
        # 验证参数类型
        if not isinstance(data['boxLocX'], (int, float)) or not isinstance(data['boxLocY'], (int, float)):
            return jsonify({'error': 'Invalid coordinate format'}), 400
        
        # 处理逻辑...
        
    except json.JSONDecodeError:
        return jsonify({'error': 'Invalid JSON format'}), 400
    except Exception as e:
        logger.error(f"Error in phoning detection: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
```

### 2. 限制请求大小

配置 Nginx 或其他反向代理，限制请求大小，防止过大的请求导致服务器负载过高：

```nginx
# 在 Nginx 配置中添加
client_max_body_size 10M;  # 限制请求大小为 10MB
```

### 3. 添加认证

为 API 添加基本认证：

```python
from flask_httpauth import HTTPBasicAuth
from werkzeug.security import generate_password_hash, check_password_hash

auth = HTTPBasicAuth()
users = {
    "admin": generate_password_hash("password")
}

@auth.verify_password
def verify_password(username, password):
    if username in users and check_password_hash(users[username], password):
        return username
    return None

@app.route('/phoning', methods=['POST'])
@auth.login_required
def phoning_detect():
    # 处理逻辑...
```

## 更新与维护

### 1. 更新模型

如需更新模型，请按照以下步骤操作：

1. 准备新的 TensorRT 模型文件
2. 停止服务
3. 替换 `1.engine` 文件
4. 重启服务

### 2. 更新代码

如需更新代码，请按照以下步骤操作：

1. 停止服务
2. 更新代码（通过 git pull 或手动替换）
3. 安装新的依赖（如有）
4. 重启服务

## 支持与联系

如有任何问题或需要技术支持，请联系系统管理员。
