# MMPose Flask 项目文档

## 项目概述

MMPose Flask 是一个基于 Flask 的 Web 服务，提供人体姿态检测和行为识别功能。该项目使用 TensorRT 优化的姿态检测模型，能够识别人体的 17 个关键点，并基于这些关键点分析特定行为，如打电话、吸烟和摔倒。

## 技术栈

- **后端框架**：Flask
- **深度学习框架**：PyTorch
- **模型优化**：TensorRT
- **图像处理**：OpenCV
- **数值计算**：NumPy

## 系统架构

```mermaid
graph LR
    A[客户端] -->|Base64图像请求| B[Flask服务]
    B -->|图像解码| C[预处理]
    C -->|模型输入| D[TensorRT推理]
    D -->|热力图| E[关键点提取]
    E -->|17个关键点| F[行为过滤器]
    F -->|判断结果| B
    B -->|JSON响应| A
```

## 核心组件

### 1. TensorRT 模型封装 (Tensorrt.py)

该模块负责将 TensorRT 引擎封装为 PyTorch 模块，使其能够与 PyTorch 生态系统无缝集成。

主要类：
- `TrtModelMMPose`：封装 TensorRT 引擎的 PyTorch 模块
- `select_device`：选择合适的计算设备（CPU 或 GPU）

### 2. 姿态检测器 (trt_model.py)

该模块实现了姿态检测的核心功能，包括图像预处理、模型推理和后处理。

主要组件：
- `preprocessing`：将输入图像转换为模型所需的格式
- `PoseDetector`：姿态检测器类，封装了预测和后处理逻辑
  - `predict`：执行模型推理
  - `postprocessing`：将模型输出的热力图转换为关键点坐标和置信度

### 3. 行为过滤器 (action_filters.py)

该模块包含多个行为过滤器，用于分析姿态关键点并判断特定行为。

主要类：
- `CommonTools`：提供计算角度、距离等通用工具方法
- `SmokingFilter`：吸烟行为检测
- `PhoningFilter`：打电话行为检测
- `FallingFilter`：摔倒行为检测

### 4. Web 服务 (service.py)

该模块实现了 Flask Web 服务，提供 REST API 接口。

主要接口：
- `/phoning`：检测打电话行为
- `/smoking`：检测吸烟行为
- `/falling`：检测摔倒行为

## 关键点定义

项目使用 17 个人体关键点，定义如下：

```
{
    "nose": 0,
    "left_eye": 1,
    "right_eye": 2,
    "left_ear": 3,
    "right_ear": 4,
    "left_shoulder": 5,
    "right_shoulder": 6,
    "left_elbow": 7,
    "right_elbow": 8,
    "left_wrist": 9,
    "right_wrist": 10,
    "left_hip": 11,
    "right_hip": 12,
    "left_knee": 13,
    "right_knee": 14,
    "left_ankle": 15,
    "right_ankle": 16
}
```

## 行为检测算法

### 吸烟检测 (SmokingFilter)

```mermaid
graph TD
    A[输入关键点和香烟位置] --> B{关键点置信度检查}
    B -->|不满足| C[返回False]
    B -->|满足| D{香烟位置检查}
    D -->|不满足| C
    D -->|满足| E[计算左右手臂角度]
    E --> F{至少一只手臂角度<60°?}
    F -->|否| C
    F -->|是| G[选择角度较小的手臂]
    G --> H{手腕在肘部与鼻子之间?}
    H -->|是| I[返回True:检测到吸烟]
    H -->|否| C
```

检测逻辑：
1. 检查关键点置信度是否足够（至少9个关键点置信度>0.2）
2. 检查香烟位置是否合理（与鼻子的距离不超过眼距的3倍，且不在鼻子上方）
3. 计算左右手臂角度（肘部-手腕与肘部-肩膀的夹角）
4. 如果至少一只手臂角度小于60°，选择角度较小的手臂
5. 检查手腕位置是否在肘部与鼻子之间，符合吸烟姿势

### 打电话检测 (PhoningFilter)

检测逻辑：
1. 检查关键点置信度是否足够（至少9个关键点置信度>0.2）
2. 检查电话位置是否合理（与耳朵中心的距离不超过肩宽的0.8倍）
3. 计算左右手臂角度（肘部-手腕与肘部-肩膀的夹角）
4. 如果至少一只手臂角度小于60°，选择角度较小的手臂
5. 检查手腕位置是否符合打电话姿势（手腕到耳朵的距离适中，且手腕到鼻子的距离较大）

### 摔倒检测 (FallingFilter)

检测逻辑：
1. 检查关键点置信度是否足够（至少9个关键点置信度>0.2）
2. 计算身体各部分与水平线的夹角
3. 计算上下身体比例
4. 根据角度和比例判断是否为摔倒姿势（上下身体与水平线夹角均小于45°，且比例在特定范围内）

## API 接口说明

### 1. 打电话检测

- **URL**: `/phoning`
- **方法**: POST
- **请求参数**:
  - `image`: Base64编码的图像
  - `boxLocX`: 电话位置X坐标
  - `boxLocY`: 电话位置Y坐标
- **响应**:
  - `result`: 布尔值，表示是否检测到打电话行为

### 2. 吸烟检测

- **URL**: `/smoking`
- **方法**: POST
- **请求参数**:
  - `image`: Base64编码的图像
  - `boxLocX`: 香烟位置X坐标
  - `boxLocY`: 香烟位置Y坐标
- **响应**:
  - `result`: 布尔值，表示是否检测到吸烟行为

### 3. 摔倒检测

- **URL**: `/falling`
- **方法**: POST
- **请求参数**:
  - `image`: Base64编码的图像
- **响应**:
  - `result`: 布尔值，表示是否检测到摔倒行为

## 模型说明

项目使用 TensorRT 优化的姿态检测模型，模型文件为 `1.engine`。该模型输入为 1×3×256×192 的张量，输出为 1×17×64×48 的热力图，表示 17 个关键点的位置概率分布。

模型处理流程：
1. 图像预处理：调整大小、转换为张量、标准化
2. 模型推理：生成热力图
3. 后处理：从热力图中提取关键点坐标和置信度

## 部署说明

服务默认运行在 `0.0.0.0:20249`，可以通过修改 `service.py` 中的相关参数进行配置。

## 性能优化

项目使用 TensorRT 进行模型优化，显著提高了推理速度。此外，代码中还包含以下优化措施：
- 模型预热：通过 `_warm_up` 方法减少首次推理的延迟
- 异步执行：使用 CUDA 流进行异步推理
- 内存优化：重用张量，减少内存分配和释放开销

## 扩展性

项目设计具有良好的扩展性，可以通过以下方式进行扩展：
1. 添加新的行为过滤器：继承 `CommonTools` 类并实现 `filter` 方法
2. 更换模型：替换 `1.engine` 文件，并根据需要调整预处理和后处理逻辑
3. 添加新的 API 接口：在 `service.py` 中添加新的路由和处理函数
