# MMPose Flask API 用户指南

## 概述

MMPose Flask API 提供了基于人体姿态检测的行为识别服务，包括打电话、吸烟和摔倒检测。本指南将帮助您了解如何使用这些 API。

## 服务地址

服务默认运行在 `http://[服务器IP]:20249`，请根据实际部署情况替换服务器 IP。

## API 列表

| 接口名称 | URL | 方法 | 功能描述 |
|---------|-----|------|---------|
| 打电话检测 | `/phoning` | POST | 检测图像中的人是否在打电话 |
| 吸烟检测 | `/smoking` | POST | 检测图像中的人是否在吸烟 |
| 摔倒检测 | `/falling` | POST | 检测图像中的人是否摔倒 |

## 请求格式

所有 API 请求均使用 JSON 格式，请求头需要设置：

```
Content-Type: application/json
```

## API 详细说明

### 1. 打电话检测

#### 请求

```http
POST /phoning HTTP/1.1
Host: [服务器IP]:20249
Content-Type: application/json

{
    "image": "base64编码的图像数据...",
    "boxLocX": 100,
    "boxLocY": 150
}
```

参数说明：
- `image`: 必填，Base64 编码的图像数据（不包含 `data:image/jpeg;base64,` 前缀）
- `boxLocX`: 必填，电话在图像中的 X 坐标
- `boxLocY`: 必填，电话在图像中的 Y 坐标

#### 响应

```json
{
    "result": true
}
```

响应说明：
- `result`: 布尔值，`true` 表示检测到打电话行为，`false` 表示未检测到

### 2. 吸烟检测

#### 请求

```http
POST /smoking HTTP/1.1
Host: [服务器IP]:20249
Content-Type: application/json

{
    "image": "base64编码的图像数据...",
    "boxLocX": 120,
    "boxLocY": 160
}
```

参数说明：
- `image`: 必填，Base64 编码的图像数据（不包含 `data:image/jpeg;base64,` 前缀）
- `boxLocX`: 必填，香烟在图像中的 X 坐标
- `boxLocY`: 必填，香烟在图像中的 Y 坐标

#### 响应

```json
{
    "result": false
}
```

响应说明：
- `result`: 布尔值，`true` 表示检测到吸烟行为，`false` 表示未检测到

### 3. 摔倒检测

#### 请求

```http
POST /falling HTTP/1.1
Host: [服务器IP]:20249
Content-Type: application/json

{
    "image": "base64编码的图像数据..."
}
```

参数说明：
- `image`: 必填，Base64 编码的图像数据（不包含 `data:image/jpeg;base64,` 前缀）

#### 响应

```json
{
    "result": true
}
```

响应说明：
- `result`: 布尔值，`true` 表示检测到摔倒行为，`false` 表示未检测到

## 图像要求

为了获得最佳检测效果，请确保：

1. 图像清晰，分辨率适中（建议至少 640×480）
2. 人物完整可见，特别是关键部位（头部、手臂、躯干）
3. 光线充足，避免过度曝光或过暗
4. 对于打电话和吸烟检测，确保电话/香烟位置坐标准确

## 示例代码

### Python 示例

```python
import requests
import base64
import json
import cv2

# 服务地址
url = "http://[服务器IP]:20249/smoking"

# 读取图像并转换为 Base64
image_path = "example.jpg"
image = cv2.imread(image_path)
_, buffer = cv2.imencode('.jpg', image)
image_base64 = base64.b64encode(buffer).decode('utf-8')

# 香烟位置（示例坐标，需要根据实际图像调整）
box_loc_x = 120
box_loc_y = 160

# 构建请求数据
data = {
    "image": image_base64,
    "boxLocX": box_loc_x,
    "boxLocY": box_loc_y
}

# 发送请求
response = requests.post(url, data=json.dumps(data))

# 解析响应
result = response.json()
print(f"检测结果: {'吸烟' if result['result'] else '未吸烟'}")
```

### JavaScript 示例

```javascript
// 读取图像文件并转换为 Base64
function readImageFile(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (event) => {
            // 移除 Base64 前缀
            const base64 = event.target.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

// 发送请求
async function detectFalling(imageFile) {
    try {
        const imageBase64 = await readImageFile(imageFile);
        
        const response = await fetch('http://[服务器IP]:20249/falling', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                image: imageBase64
            })
        });
        
        const result = await response.json();
        console.log(`检测结果: ${result.result ? '摔倒' : '未摔倒'}`);
        return result.result;
    } catch (error) {
        console.error('请求失败:', error);
        throw error;
    }
}

// 使用示例
const fileInput = document.getElementById('imageInput');
fileInput.addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
        const isFalling = await detectFalling(file);
        alert(isFalling ? '检测到摔倒!' : '未检测到摔倒');
    }
});
```

## 错误处理

API 可能返回以下错误：

| HTTP 状态码 | 可能原因 |
|------------|---------|
| 400 | 请求参数错误，如缺少必要参数或参数格式不正确 |
| 500 | 服务器内部错误，如模型推理失败 |

## 性能考虑

1. 图像大小：较大的图像会增加传输和处理时间，建议在客户端适当压缩图像
2. 请求频率：服务器处理每个请求需要一定时间，建议控制请求频率
3. 并发请求：服务器可能有并发处理能力限制，建议在高并发场景下进行负载测试

## 常见问题

### Q: 为什么检测结果不准确？

A: 检测准确性受多种因素影响，包括：
- 图像质量不佳
- 人物姿态不明显或被遮挡
- 电话/香烟位置坐标不准确
- 光线条件不佳

### Q: 如何提高检测准确率？

A: 
- 确保图像清晰，光线充足
- 人物姿态明显，关键部位可见
- 准确标记电话/香烟位置
- 对于边界情况，可以尝试调整姿态使其更符合典型的行为模式

### Q: API 响应时间较长怎么办？

A:
- 减小图像尺寸
- 检查网络连接
- 考虑在服务器端进行性能优化或硬件升级

## 联系支持

如有任何问题或需要技术支持，请联系系统管理员。
