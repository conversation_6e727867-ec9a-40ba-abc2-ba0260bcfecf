# 打电话姿态估计处理流程

本文档详细说明了MMPose Flask项目中打电话行为检测的姿态估计前处理和后处理流程。

## 1. 姿态估计前处理

在打电话行为检测中，姿态估计前处理是指将输入图像转换为适合深度学习模型处理的格式。这个过程包括图像加载、预处理和模型输入准备。

### 1.1 图像加载

首先，从API请求中获取Base64编码的图像和电话位置坐标：

```python
# 从请求中获取数据
data = json.loads(request.data)
img_base64 = data['image']
pointx = int(data['boxLocX'])
pointy = int(data['boxLocY'])
center_point = [pointx, pointy]

# 解码Base64图像
img_binary = base64.b64decode(img_base64)
nparr = np.frombuffer(img_binary, np.uint8)
img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

# 获取图像尺寸
h, w, _ = img.shape
```

这个过程完成了以下工作：
1. 解析JSON请求数据，提取Base64编码的图像和电话位置坐标
2. 将Base64字符串解码为二进制数据
3. 将二进制数据转换为NumPy数组
4. 使用OpenCV解码图像并转换为RGB格式
5. 获取图像的高度和宽度

### 1.2 图像预处理

图像加载后，需要进行预处理，将其转换为模型所需的格式。这个过程在`preprocessing`函数中实现：

```python
def preprocessing(ori_img):
    img_transform = transforms.Compose(
        [
            transforms.ToPILImage(),  # 转换为PIL图像
            transforms.Resize((256, 192)),  # 调整大小
            transforms.ToTensor(),  # 转换为张量
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])  # 标准化
        ])

    img = img_transform(ori_img)
    img = torch.unsqueeze(img, dim=0).cuda()  # 添加批次维度并移动到GPU

    return img
```

预处理步骤包括：
1. **转换为PIL图像**：将OpenCV格式的图像（NumPy数组）转换为PIL图像，以便使用torchvision的变换操作
2. **调整大小**：将图像调整为模型所需的输入尺寸（256×192像素）
3. **转换为张量**：将PIL图像转换为PyTorch张量，同时将像素值从[0, 255]范围缩放到[0, 1]范围
4. **标准化**：使用ImageNet数据集的均值和标准差对张量进行标准化
   - 均值：[0.485, 0.456, 0.406]
   - 标准差：[0.229, 0.224, 0.225]
5. **添加批次维度**：使用`torch.unsqueeze`添加批次维度，将形状从(3, 256, 192)变为(1, 3, 256, 192)
6. **移动到GPU**：使用`.cuda()`将张量移动到GPU内存，加速计算

### 1.3 模型输入准备

预处理后的图像被封装为模型所需的输入格式，并传递给模型进行推理：

```python
def predict(self, img):
    img0 = preprocessing(img)  # 预处理图像
    pred = self.model(dict(input=img0))  # 执行推理
    return pred
```

这个过程包括：
1. 调用`preprocessing`函数对图像进行预处理
2. 将预处理后的图像封装为字典格式`dict(input=img0)`
3. 将字典传递给模型进行推理

## 2. 姿态估计后处理

姿态估计后处理是指将模型输出的热力图转换为关键点坐标和置信度，并基于这些信息判断是否为打电话行为。

### 2.1 热力图后处理

模型输出的是17个关键点的热力图，需要通过后处理将其转换为具体的坐标和置信度：

```python
def postprocessing(self, outputs, ori_img_w, ori_img_h):
    """得到热力图后的后处理操作

    Args:
        outputs (tensor): 1x17x48x64
        ori_img_w (int): 原始图片的宽
        ori_img_h (int): 原始图片的高
    Return:
        keypoints: 二维list, 包含17个关键点坐标, [[x1,y1], [x2,y2], ...]
        scores: 一维list, 对应的17个关键点的分数
    """
    keypoints = []
    scores = []
    w, h = ori_img_w, ori_img_h
    
    for i in range(17):  # 处理17个关键点
        heatmap = outputs[0, i, :, :]  # 获取第i个关键点的热力图
        heatmap_flatten = heatmap.view(-1)  # 展平热力图
        max_value, max_index = torch.max(heatmap_flatten, 0)  # 找到最大值及其索引
        scores.append(max_value.tolist())  # 记录置信度
        
        # 计算热力图中最大值位置的坐标
        heatmap_loc = [max_index.cpu().numpy() // heatmap.size(1), max_index.cpu().numpy() % heatmap.size(1)]
        
        # 将热力图坐标映射回原始图像坐标
        keypoints_x = heatmap_loc[1] * (256 / 64) * (w / 192)
        keypoints_y = heatmap_loc[0] * (192 / 48) * (h / 256)
        keypoints.append([keypoints_x, keypoints_y])

    return keypoints, scores
```

后处理步骤包括：
1. **初始化结果列表**：创建空列表`keypoints`和`scores`，用于存储关键点坐标和置信度
2. **获取原始图像尺寸**：记录原始图像的宽度`w`和高度`h`，用于坐标映射
3. **处理每个关键点**：对于每个关键点（共17个），执行以下操作：
   - **提取热力图**：从输出张量中提取第i个关键点的热力图，形状为(48, 64)
   - **展平热力图**：将热力图展平为一维张量，便于找到最大值
   - **找到最大值及其索引**：使用`torch.max()`找到热力图中的最大值和对应的索引
   - **记录置信度**：将最大值作为关键点的置信度，添加到`scores`列表中
   - **计算热力图坐标**：将一维索引转换为二维坐标
     - 行索引 = `max_index // heatmap.size(1)`
     - 列索引 = `max_index % heatmap.size(1)`
   - **映射到原始图像坐标**：将热力图坐标映射回原始图像坐标系
     - X坐标：`keypoints_x = heatmap_loc[1] * (256 / 64) * (w / 192)`
     - Y坐标：`keypoints_y = heatmap_loc[0] * (192 / 48) * (h / 256)`
   - **添加关键点坐标**：将计算得到的坐标添加到`keypoints`列表中
4. **返回结果**：返回关键点坐标列表和置信度列表

### 2.2 坐标映射原理

坐标映射涉及两次比例变换：
1. **热力图到模型输入的映射**：
   - 热力图尺寸为(48, 64)，模型输入尺寸为(256, 192)
   - 高度比例：256 / 48 = 5.33
   - 宽度比例：192 / 64 = 3
2. **模型输入到原始图像的映射**：
   - 模型输入尺寸为(256, 192)，原始图像尺寸为(h, w)
   - 高度比例：h / 256
   - 宽度比例：w / 192
3. **综合映射**：
   - X坐标：`heatmap_x * (256 / 64) * (w / 192) = heatmap_x * (w / 48)`
   - Y坐标：`heatmap_y * (192 / 48) * (h / 256) = heatmap_y * (h / 64)`

### 2.3 关键点定义

模型输出的17个关键点定义如下：
```
{
    "nose": 0,
    "left_eye": 1,
    "right_eye": 2,
    "left_ear": 3,
    "right_ear": 4,
    "left_shoulder": 5,
    "right_shoulder": 6,
    "left_elbow": 7,
    "right_elbow": 8,
    "left_wrist": 9,
    "right_wrist": 10,
    "left_hip": 11,
    "right_hip": 12,
    "left_knee": 13,
    "right_knee": 14,
    "left_ankle": 15,
    "right_ankle": 16
}
```

## 3. 打电话行为判断

获取关键点坐标和置信度后，使用`PhoningFilter`类判断是否为打电话行为：

```python
class PhoningFilter(CommonTools):
    def __init__(self, keypoints, scores:list, center_point):
        super().__init__()
        self.phoning_threshold = 0.2
        self.keypoints = np.array(keypoints)
        self.scores = scores
        self.center_point = np.array(center_point)

    def set_score_thresh(self, thresh):
        self.phoning_threshold = thresh

    def filter(self, angle_thresh=60):
        # 检查关键点置信度
        count = sum(1 for i in self.scores if i > self.phoning_threshold)
        if count > 9:
            # 电话中心和耳朵位置判断
            ears_center = (self.keypoints[3] + self.keypoints[4]) / 2
            phone_ear_dis = self.calculate_distance(self.center_point, ears_center)
            shoulder_dis = self.calculate_distance(self.keypoints[6], self.keypoints[5])
            # 电话距离耳朵远，不报警
            if phone_ear_dis > shoulder_dis * 0.8:
                return False
            # 计算向量夹角
            left_elbow_wrist_vec = self.keypoints[7] - self.keypoints[9]
            right_elbow_wrist_vec = self.keypoints[8] - self.keypoints[10]
            left_elbow_shoulder_vec = self.keypoints[7] - self.keypoints[5]
            right_elbow_shoulder_vec = self.keypoints[8] - self.keypoints[6]

            left_angle = self.calculate_angle(left_elbow_wrist_vec, left_elbow_shoulder_vec)
            right_angle = self.calculate_angle(right_elbow_wrist_vec, right_elbow_shoulder_vec)

            # 打电话时，有一只手的关节夹角小于60度，并且wrist-ear很小, wrist-nose较大
            if left_angle < angle_thresh and left_angle < right_angle:
                ear_wrist_dis = self.calculate_distance(self.keypoints[3], self.keypoints[9])
                wrist_nose_dis = self.calculate_distance(self.keypoints[0], self.keypoints[9])
                wrist_elbow_dis = self.calculate_distance(self.keypoints[7], self.keypoints[9])

            elif right_angle < angle_thresh and right_angle < left_angle:
                ear_wrist_dis = self.calculate_distance(self.keypoints[4], self.keypoints[10])
                wrist_nose_dis = self.calculate_distance(self.keypoints[0], self.keypoints[10])
                wrist_elbow_dis = self.calculate_distance(self.keypoints[8], self.keypoints[10])
            else:
                return False
            
            if 0.5 * wrist_elbow_dis < ear_wrist_dis < wrist_nose_dis:
                return True 
            else:
                return False

        else:
            return False
```

打电话行为判断的步骤包括：

1. **检查关键点置信度**：
   - 计算置信度大于阈值（默认0.2）的关键点数量
   - 如果少于9个关键点置信度足够，则认为检测不可靠，返回False

2. **电话位置判断**：
   - 计算耳朵中心点（左耳和右耳的平均位置）
   - 计算电话中心点到耳朵中心点的距离
   - 计算肩膀宽度（左肩到右肩的距离）
   - 如果电话距离耳朵太远（大于肩宽的0.8倍），则不是打电话行为，返回False

3. **手臂角度计算**：
   - 计算左手臂向量：
     - 肘部到手腕向量：`left_elbow_wrist_vec = keypoints[7] - keypoints[9]`
     - 肘部到肩膀向量：`left_elbow_shoulder_vec = keypoints[7] - keypoints[5]`
   - 计算右手臂向量：
     - 肘部到手腕向量：`right_elbow_wrist_vec = keypoints[8] - keypoints[10]`
     - 肘部到肩膀向量：`right_elbow_shoulder_vec = keypoints[8] - keypoints[6]`
   - 计算左右手臂的肘部角度（肘部-手腕与肘部-肩膀的夹角）

4. **选择角度较小的手臂**：
   - 如果左手臂角度小于阈值（默认60度）且小于右手臂角度，选择左手臂
   - 如果右手臂角度小于阈值且小于左手臂角度，选择右手臂
   - 如果两只手臂角度都大于阈值，则不是打电话行为，返回False

5. **手腕位置判断**：
   - 计算手腕到耳朵的距离：`ear_wrist_dis`
   - 计算手腕到鼻子的距离：`wrist_nose_dis`
   - 计算手腕到肘部的距离：`wrist_elbow_dis`
   - 判断条件：`0.5 * wrist_elbow_dis < ear_wrist_dis < wrist_nose_dis`
     - 手腕到耳朵的距离应该大于手腕到肘部距离的一半（手不能离耳朵太远）
     - 手腕到耳朵的距离应该小于手腕到鼻子的距离（手应该更靠近耳朵而不是鼻子）
   - 如果满足条件，则判断为打电话行为，返回True
   - 否则，返回False

## 4. 完整流程

打电话姿态估计的完整流程如下：

```python
@app.route('/phoning', methods=['POST'])
def phoning_detect():
    start = time.time()

    # 1. 图像加载和前处理
    data = json.loads(request.data)
    img_base64 = data['image']
    pointx = int(data['boxLocX'])
    pointy = int(data['boxLocY'])
    center_point = [pointx, pointy]
    img_binary = base64.b64decode(img_base64)
    nparr = np.frombuffer(img_binary, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

    # 获取图像尺寸
    h, w, _ = img.shape
    
    # 2. 模型推理
    outs = pose_detector.predict(img)
    
    # 3. 热力图后处理
    k, s = pose_detector.postprocessing(outs, w, h)

    # 4. 打电话行为判断
    pfilter = PhoningFilter(k, s, center_point)
    is_phoning = pfilter.filter()

    end = time.time()
    print('phoning filter time cost: ', end - start)

    # 5. 返回结果
    return jsonify({'result': is_phoning})
```

## 5. 总结

打电话姿态估计的前处理和后处理流程可以总结为以下几个关键步骤：

### 前处理流程
1. **图像加载**：解码Base64图像，转换为RGB格式
2. **图像预处理**：调整大小、标准化、转换为张量
3. **模型输入准备**：添加批次维度，移动到GPU

### 后处理流程
1. **热力图处理**：从热力图中提取关键点坐标和置信度
2. **坐标映射**：将热力图坐标映射回原始图像坐标
3. **打电话行为判断**：
   - 检查关键点置信度
   - 判断电话位置
   - 计算手臂角度
   - 判断手腕位置
   - 综合判断是否为打电话行为

通过这一系列处理，系统能够准确地检测出图像中的人是否在打电话，为安全监控、行为分析等应用提供支持。
