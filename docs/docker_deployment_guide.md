# MMPose Flask 服务 Docker 部署指南

## 概述

本项目可以通过Docker进行容器化部署，但**必须要求宿主机有NVIDIA GPU和相关驱动支持**。

## 系统要求

### 硬件要求

- NVIDIA GPU (支持CUDA 10.2+)
- 至少4GB GPU内存
- 至少8GB系统内存
- 至少20GB磁盘空间

### 软件要求

- Docker Engine 20.10+
- Docker Compose 2.0+
- NVIDIA Container Toolkit (nvidia-docker2)
- NVIDIA GPU驱动 450.80.02+
- CUDA 11.0+

## 安装NVIDIA Container Toolkit

### Ubuntu/Debian

```bash
# 添加NVIDIA Docker仓库
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

# 安装nvidia-container-toolkit
sudo apt-get update
sudo apt-get install -y nvidia-container-toolkit

# 重启Docker
sudo systemctl restart docker
```

### CentOS/RHEL

```bash
# 添加仓库
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.repo | sudo tee /etc/yum.repos.d/nvidia-docker.repo

# 安装
sudo yum install -y nvidia-container-toolkit

# 重启Docker
sudo systemctl restart docker
```

## 验证GPU支持

```bash
# 测试NVIDIA Docker支持
docker run --rm --gpus all nvidia/cuda:11.2-base nvidia-smi
```

## 部署步骤

### 1. 克隆项目并准备文件

```bash
# 确保项目目录包含以下关键文件
ls -la
# 应该包含：
# - service.py
# - trt_model.py
# - Tensorrt.py
# - action_filters.py
# - 1.engine (TensorRT模型文件)
# - Dockerfile
# - docker-compose.yml
```

### 2. 创建必要目录

```bash
mkdir -p logs temp models monitoring/grafana monitoring/prometheus
```

### 3. 构建并启动服务

```bash
# 基础部署（仅服务）
docker-compose up -d

# 带Nginx负载均衡的部署
docker-compose --profile with-nginx up -d

# 带监控的完整部署
docker-compose --profile monitoring --profile with-nginx up -d
```

### 4. 验证部署

```bash
# 检查容器状态
docker-compose ps

# 查看日志
docker-compose logs -f mmpose-flask

# 健康检查
curl http://localhost:20249/health

# API测试
curl -X POST http://localhost:20249/phoning \
  -H "Content-Type: application/json" \
  -d '{
    "image": "base64_encoded_image_data",
    "boxLocX": 100,
    "boxLocY": 150
  }'
```

## 配置选项

### 环境变量配置

在`docker-compose.yml`中可以调整以下环境变量：

```yaml
environment:
  - CUDA_VISIBLE_DEVICES=0          # 指定使用的GPU
  - FLASK_ENV=production            # Flask环境
  - TRT_LOGGER_LEVEL=WARNING        # TensorRT日志级别
  - PYTHONUNBUFFERED=1              # Python输出不缓冲
```

### 资源限制

```yaml
deploy:
  resources:
    limits:
      memory: 8G      # 最大内存使用
      cpus: '4.0'     # 最大CPU核数
    reservations:
      devices:
        - driver: nvidia
          count: 1      # 预留GPU数量
          capabilities: [gpu]
```

### 端口映射

- `20249`: Flask API服务端口
- `80`: Nginx代理端口（如果启用）
- `3000`: Grafana监控界面（如果启用）
- `9090`: Prometheus监控（如果启用）

## 生产环境优化

### 1. 多实例部署

```bash
# 扩展到3个实例
docker-compose up -d --scale mmpose-flask=3
```

### 2. 资源监控

启用监控profile后，可通过以下地址访问：

- Grafana: http://localhost:3000 (admin/admin123)
- Prometheus: http://localhost:9090

### 3. 日志管理

```bash
# 查看实时日志
docker-compose logs -f

# 日志轮转配置在docker-compose.yml中
logging:
  options:
    max-size: "100m"
    max-file: "3"
```

### 4. 性能优化建议

#### GPU优化

```bash
# 设置GPU性能模式
sudo nvidia-smi -pm 1
sudo nvidia-smi -ac 5001,1590  # 根据GPU型号调整
```

#### Docker优化

```bash
# 在/etc/docker/daemon.json中添加
{
  "default-runtime": "nvidia",
  "runtimes": {
    "nvidia": {
      "path": "nvidia-container-runtime",
      "runtimeArgs": []
    }
  }
}
```

## 故障排除

### 常见问题

#### 1. GPU不可用

```bash
# 检查GPU状态
nvidia-smi

# 检查Docker GPU支持
docker run --rm --gpus all nvidia/cuda:11.2-base nvidia-smi

# 检查容器内GPU
docker exec -it mmpose-flask-service nvidia-smi
```

#### 2. 内存不足

```bash
# 监控GPU内存使用
nvidia-smi -l 1

# 调整Docker内存限制
# 在docker-compose.yml中增加memory限制
```

#### 3. 模型加载失败

```bash
# 检查模型文件
docker exec -it mmpose-flask-service ls -la /app/1.engine

# 检查文件权限
docker exec -it mmpose-flask-service file /app/1.engine
```

#### 4. 端口冲突

```bash
# 检查端口占用
netstat -tulpn | grep 20249

# 修改docker-compose.yml中的端口映射
ports:
  - "8080:20249"  # 映射到8080端口
```

### 调试命令

```bash
# 进入容器调试
docker exec -it mmpose-flask-service bash

# 查看容器资源使用
docker stats mmpose-flask-service

# 查看容器详细信息
docker inspect mmpose-flask-service

# 重启服务
docker-compose restart mmpose-flask
```

## 更新部署

### 代码更新

```bash
# 重新构建镜像
docker-compose build --no-cache

# 停止并重启服务
docker-compose down
docker-compose up -d
```

### 模型更新

```bash
# 将新的1.engine文件放到项目目录
cp new_model.engine 1.engine

# 重启容器
docker-compose restart mmpose-flask
```

## 备份与恢复

### 数据备份

```bash
# 备份日志
docker cp mmpose-flask-service:/app/logs ./backup/logs-$(date +%Y%m%d)

# 备份配置
tar -czf backup/config-$(date +%Y%m%d).tar.gz docker-compose.yml nginx.conf Dockerfile
```

### 恢复部署

```bash
# 恢复配置
tar -xzf backup/config-YYYYMMDD.tar.gz

# 重新部署
docker-compose up -d
```

## 安全建议


1. **网络安全**: 在生产环境中通过防火墙限制访问端口
2. **SSL/TLS**: 使用Nginx配置HTTPS证书
3. **认证授权**: 为API添加身份认证机制
4. **镜像安全**: 定期更新基础镜像
5. **日志审计**: 启用详细的访问日志记录

## 联系支持

如遇到部署问题，请提供以下信息：

- 系统环境信息 (`uname -a`)
- GPU信息 (`nvidia-smi`)
- Docker版本 (`docker --version`)
- 容器日志 (`docker-compose logs`)
- 错误截图或完整错误信息


