# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Flask-based web service for real-time human pose detection and behavior recognition, specifically designed to detect phone calls, smoking, and falling behaviors using TensorRT-optimized deep learning models.

## Project Structure

```
mmpose_flask/
├── src/                      # Source code
│   ├── service.py           # Flask web server (port 20249)
│   ├── trt_model.py         # Pose detector wrapper using TensorRT
│   ├── Tensorrt.py          # Low-level TensorRT engine wrapper
│   ├── action_filters.py    # Behavior recognition filters
│   └── cyddh_filter.py      # Alternative filter implementation
├── models/                   # Model files
│   └── 1.engine            # Pre-trained TensorRT model (required)
├── docs/                     # Documentation
│   ├── technical_documentation.md
│   ├── api_user_guide.md
│   ├── deployment_guide.md
│   ├── MMPose_TensorRT_Technical_Documentation.md
│   ├── TensorRT_Python_SDK_Documentation.md
│   └── keypoints.jpg       # Visualization example
├── deployment/              # Deployment configurations
│   ├── Dockerfile
│   ├── docker-compose.yml
│   ├── deploy.sh           # Deployment script
│   ├── nginx.conf          # Load balancer config
│   └── requirements.txt    # Python dependencies
├── diagrams/                # UML and system diagrams
│   └── plantuml_*.txt      # PlantUML diagram sources
└── tests/                   # Test files (empty)
```

## Architecture

### Core Components

- **`src/service.py`**: Flask web server (port 20249) with 3 REST API endpoints for behavior detection
- **`src/trt_model.py`**: Pose detector wrapper using TensorRT engine for inference
- **`src/Tensorrt.py`**: Low-level TensorRT engine wrapper for PyTorch integration  
- **`src/action_filters.py`**: Behavior recognition filters (smoking, phoning, falling)
- **`models/1.engine`**: Pre-trained TensorRT model file (required for inference)

### Request Flow

1. Client sends Base64 image to API endpoint
2. Image decoded and preprocessed (256×192 normalization)
3. TensorRT model inference produces 17 keypoints with confidence scores
4. Behavior filter analyzes keypoint patterns
5. Boolean result returned

### Keypoint Model

Uses standard 17-point human skeleton: nose, eyes, ears, shoulders, elbows, wrists, hips, knees, ankles.

## Development Commands

**Run the service:**
```bash
cd src
python service.py
```

**Docker deployment:**
```bash
cd deployment
./deploy.sh
```

**Available deployment configurations in `deployment/` directory** - includes Dockerfile, docker-compose.yml, and deployment scripts.

## API Endpoints

- `POST /phoning` - Phone call detection (requires boxLocX, boxLocY)
- `POST /smoking` - Smoking detection (requires boxLocX, boxLocY) 
- `POST /falling` - Fall detection

All endpoints accept JSON with `image` (base64) and optional box coordinates, return `{"result": true/false}`.

## System Requirements

- NVIDIA GPU with CUDA 10.2+
- TensorRT 7.0+
- Python 3.8+
- OpenCV, PyTorch, NumPy

## Adding New Behavior Detection

1. Inherit from `CommonTools` class in `src/action_filters.py`
2. Implement `filter()` method with keypoint analysis logic
3. Add new route in `src/service.py` following existing pattern
4. Test with sample images containing target behavior

## Performance Considerations

- TensorRT provides significant inference acceleration
- Model uses async CUDA streams for GPU efficiency
- First inference includes model warmup latency
- Memory pre-allocation avoids dynamic allocation overhead

## Deployment Notes

- Service binds to `0.0.0.0:20249` by default
- GPU dependency makes this unsuitable for CPU-only environments
- Consider Gunicorn + Nginx for production deployment
- Docker deployment requires NVIDIA container runtime
- See `deployment/` directory for complete Docker setup and scripts