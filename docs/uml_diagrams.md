# MMPose Flask 项目 UML 图

## 1. 类图

```mermaid
classDiagram
    %% 核心类
    class Flask {
        +run(host, port)
        +route(rule, methods)
    }
    
    class torch.nn.Module {
        +forward(inputs)
    }
    
    %% TensorRT 相关类
    class TrtModelMMPose {
        -engine: trt.ICudaEngine
        -context: trt.IExecutionContext
        -_input_names: list
        -_output_names: list
        +__init__(engine, output_names)
        +forward(inputs: Dict) Dict
    }
    
    %% 姿态检测器类
    class PoseDetector {
        -input_shape: list
        -device: torch.device
        -weights: str
        -model: TrtModelMMPose
        +__init__(weights, device)
        +_warm_up()
        +predict(img) tensor
        +postprocessing(outputs, ori_img_w, ori_img_h) tuple
    }
    
    %% 行为过滤器类
    class CommonTools {
        +__init__()
        +calculate_angle(vecA, vecB) float
        +calculate_distance(pointA, pointB) float
        +judge_position()
    }
    
    class SmokingFilter {
        -smoking_threshold: float
        -keypoints: np.array
        -scores: list
        -center_point: np.array
        +__init__(keypoints, scores, center_point)
        +set_score_thresh(thresh)
        +filter(angle_thresh) bool
    }
    
    class PhoningFilter {
        -phoning_threshold: float
        -keypoints: np.array
        -scores: list
        -center_point: np.array
        +__init__(keypoints, scores, center_point)
        +set_score_thresh(thresh)
        +filter(angle_thresh) bool
    }
    
    class FallingFilter {
        -falling_threshold: float
        -keypoints: np.array
        -scores: list
        +__init__(keypoints, scores)
        +set_score_thresh(thresh)
        +filter(angle_thresh) bool
    }
    
    %% 关系
    torch.nn.Module <|-- TrtModelMMPose : 继承
    
    CommonTools <|-- SmokingFilter : 继承
    CommonTools <|-- PhoningFilter : 继承
    CommonTools <|-- FallingFilter : 继承
    
    PoseDetector o-- TrtModelMMPose : 组合
    
    Flask -- PoseDetector : 关联
    Flask -- SmokingFilter : 关联
    Flask -- PhoningFilter : 关联
    Flask -- FallingFilter : 关联
```

## 2. 序列图 - 打电话检测流程

```mermaid
sequenceDiagram
    participant Client
    participant Flask as Flask服务
    participant PoseDetector
    participant TrtModelMMPose
    participant PhoningFilter
    
    Client->>Flask: POST /phoning 请求
    Note over Flask: 解析请求数据
    Flask->>Flask: 解码Base64图像
    
    Flask->>PoseDetector: predict(img)
    PoseDetector->>PoseDetector: preprocessing(img)
    PoseDetector->>TrtModelMMPose: forward(dict(input=img0))
    TrtModelMMPose-->>PoseDetector: 返回热力图
    
    Flask->>PoseDetector: postprocessing(outs, w, h)
    PoseDetector-->>Flask: 返回关键点坐标和置信度
    
    Flask->>PhoningFilter: 创建PhoningFilter(k, s, center_point)
    Flask->>PhoningFilter: filter()
    Note over PhoningFilter: 检查关键点置信度
    Note over PhoningFilter: 判断电话位置
    Note over PhoningFilter: 计算手臂角度
    Note over PhoningFilter: 判断手腕位置
    PhoningFilter-->>Flask: 返回是否打电话
    
    Flask-->>Client: 返回JSON响应
```

## 3. 组件图

```mermaid
graph TD
    subgraph 客户端
        A[客户端应用]
    end
    
    subgraph Flask服务
        B[Flask应用]
        C[姿态检测器]
        D[行为过滤器]
    end
    
    subgraph 深度学习模型
        E[TensorRT引擎]
    end
    
    A -->|HTTP请求| B
    B -->|调用| C
    C -->|使用| E
    B -->|调用| D
    D -->|分析关键点| D
    B -->|HTTP响应| A
```

## 4. 部署图

```mermaid
graph TD
    subgraph 客户端设备
        A[客户端应用]
    end
    
    subgraph 服务器
        subgraph Web服务
            B[Nginx]
            C[Gunicorn]
            D[Flask应用]
        end
        
        subgraph 计算资源
            E[GPU]
            F[TensorRT]
        end
    end
    
    A -->|HTTP请求| B
    B -->|反向代理| C
    C -->|WSGI| D
    D -->|调用| F
    F -->|使用| E
```

## 5. 活动图 - 行为检测流程

```mermaid
graph TD
    A[接收HTTP请求] --> B[解析JSON数据]
    B --> C[解码Base64图像]
    C --> D[图像预处理]
    D --> E[模型推理]
    E --> F[热力图后处理]
    F --> G[提取关键点坐标和置信度]
    
    G --> H{检查关键点置信度}
    H -->|不足| I[返回False]
    H -->|足够| J{检查物体位置}
    
    J -->|不合理| I
    J -->|合理| K[计算手臂角度]
    
    K --> L{角度小于阈值?}
    L -->|否| I
    L -->|是| M[判断手腕位置]
    
    M --> N{位置合理?}
    N -->|否| I
    N -->|是| O[返回True]
    
    O --> P[返回JSON响应]
    I --> P
```

## 6. 状态图 - 姿态检测器状态

```mermaid
stateDiagram-v2
    [*] --> 初始化
    初始化 --> 就绪: 加载模型
    就绪 --> 预处理: 接收图像
    预处理 --> 推理: 转换为张量
    推理 --> 后处理: 生成热力图
    后处理 --> 就绪: 返回关键点
```

## 7. 用例图

```mermaid
graph TD
    subgraph 用户
        A[客户端用户]
    end
    
    subgraph 系统用例
        B[检测打电话行为]
        C[检测吸烟行为]
        D[检测摔倒行为]
    end
    
    A -->|使用| B
    A -->|使用| C
    A -->|使用| D
    
    subgraph 系统功能
        E[姿态检测]
        F[关键点提取]
        G[行为分析]
    end
    
    B -->|依赖| E
    C -->|依赖| E
    D -->|依赖| E
    
    E -->|包含| F
    F -->|包含| G
```
